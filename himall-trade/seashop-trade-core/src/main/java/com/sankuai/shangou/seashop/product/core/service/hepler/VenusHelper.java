package com.sankuai.shangou.seashop.product.core.service.hepler;

import java.io.File;
import java.net.URI;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/02/20 10:03
 */
@Component
@Slf4j
public class VenusHelper {


    @Value("${venus.bucket}")
    public String bucket;
    @Value("${venus.hostName}")
    public String hostName;

    /**
     * 上传导入的图片到venus 并返回名称和远程地址的映射map
     *
     * @param imgFiles 图片文件
     * @return 映射map
     */
    public Map<String, String> uploadImages(List<File> imgFiles) {
        if (CollectionUtils.isEmpty(imgFiles)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, String> remoteFileMapping = new LinkedHashMap<>();
        imgFiles.forEach(imgFile -> {
            /*ImageResult imageResult = venusService.uploadImage(imgFile);
            AssertUtil.throwIfTrue(!imageResult.isSuccess(), "图片上传失败");
            remoteFileMapping.put(imgFile.getName(), getFinalName(imageResult.getFileKey()));*/
        });
        return remoteFileMapping;
    }

    /**
     * 上传远程图片到venus 并返回名称和远程地址的映射map
     *
     * @param imgUrls 图片url
     * @return 映射map
     */
    public Map<String, String> uploadRemoteImages(List<String> imgUrls) {
        if (CollectionUtils.isEmpty(imgUrls)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, String> remoteFileMapping = new LinkedHashMap<>();
        imgUrls.forEach(imageUrl -> {
            /*byte[] bytes = HttpUtil.downloadBytes(imageUrl);
            ImageResult imageResult = venusService.uploadImage(bytes, imageUrl);
            AssertUtil.throwIfTrue(!imageResult.isSuccess(), "图片上传失败");
            remoteFileMapping.put(imageUrl, getFinalName(imageResult.getFileKey()));*/
        });
        return remoteFileMapping;
    }

    private byte[] downloadBytes(String url) {
        try {
            // 创建HttpRequest对象，并设置超时时间
            HttpRequest httpRequest = HttpRequest.get(url);
            httpRequest.timeout(5000);

            // 发送请求并获取响应
            HttpResponse httpResponse = httpRequest.execute();

            // 检查响应状态码，200表示成功
            if (!httpResponse.isOk()) {
                throw new BusinessException("下载图片失败，状态码：" + httpResponse.getStatus());
            }
            return httpResponse.bodyBytes();
        } catch (Exception e) {
            log.error("下载图片失败", e);
            throw new BusinessException("下载图片失败, 请检查图片白名单");
        }
    }

    private String getFinalName(String fileKey) {
        return fileKey.replace("/" + bucket + "/", StrUtil.EMPTY);
    }


    public String getFullPath(String path) {
        if (StringUtils.isNotEmpty(URI.create(path).getHost())) {
            return path;
        }

        return hostName + bucket + StrUtil.SLASH + path;
    }
}
