package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteFreightAreaService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 校验运费模板
 *
 * <AUTHOR>
 * @date 2024/03/04 12:03
 */
@Component
@Slf4j
public class CheckFreightTemplateHandler extends AbsProductHandler {

    @Resource
    private RemoteFreightAreaService remoteFreightAreaService;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验运费模板属性【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();
        QueryFreightTemplateDto template = remoteFreightAreaService.queryTplByTemplateId(saveProductBo.getFreightTemplateId());
        AssertUtil.throwIfNull(template, ProductResultEnum.FREIGHT_TEMPLATE_NOT_EXIST);

        Integer freightTemplateIdMethod = saveProductBo.getFreightTemplateIdMethod();
        if (freightTemplateIdMethod == null) {
            return;
        }
        AssertUtil.throwIfTrue(!freightTemplateIdMethod.equals(template.getValuationMethod()), ProductResultEnum.FREIGHT_TEMPLATE_CHANGED);
        saveProductBo.setFreightTemplateName(template.getName());

        log.info("【保存商品】校验运费模板属性【end】, context:{}", context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getFreightTemplateId() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_FREIGHT_TEMPLATE;
    }
}
