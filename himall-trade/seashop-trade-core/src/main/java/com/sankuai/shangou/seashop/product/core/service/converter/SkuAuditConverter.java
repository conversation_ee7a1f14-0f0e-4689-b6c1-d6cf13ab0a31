package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.hepler.SkuIdHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;

/**
 * <AUTHOR>
 * @date 2023/11/16 22:57
 */
public class SkuAuditConverter {

    @SuppressWarnings("all")
    public static ProductSkuBo convertToBProductSkuBo(SkuAudit sku, SkuStockAudit skuStock) {
        return Optional.ofNullable(sku).map(item -> {
            ProductSkuBo skuBo = JsonUtil.copy(item, ProductSkuBo.class);

            SkuIdHelper.SkuIdDto skuIdDto = SkuIdHelper.getSkuIdDto(item.getSkuId());
            skuBo.setSkuAutoId(item.getId());
            skuBo.setSkuId(skuIdDto.getSkuId());
            skuBo.setSpec1ValueId(skuIdDto.getSpec1ValueId());
            skuBo.setSpec2ValueId(skuIdDto.getSpec2ValueId());
            skuBo.setSpec3ValueId(skuIdDto.getSpec3ValueId());
            if (skuStock != null) {
                skuBo.setStock(skuStock.getStock());
                skuBo.setSafeStock(skuStock.getSafeStock());
            }
            return skuBo;
        }).orElse(null);
    }

    public static SkuAudit convertToSku(ProductSkuBo productSkuBo, Long productId, Long shopId) {
        return Optional.ofNullable(productSkuBo).map(item -> {
            SkuAudit sku = JsonUtil.copy(productSkuBo, SkuAudit.class);
            sku.setId(item.getSkuAutoId());
            sku.setShopId(shopId);
            sku.setProductId(productId);
            return sku;
        }).orElse(null);
    }

    public static List<SkuAudit> convertToSku(List<ProductSkuBo> productSkuBoList, Long productId, Long shopId) {
        if (CollectionUtils.isEmpty(productSkuBoList)) {
            return Collections.EMPTY_LIST;
        }

        List<SkuAudit> skuList = new ArrayList<>();
        productSkuBoList.forEach(bo -> {
            skuList.add(convertToSku(bo, productId, shopId));
        });
        return skuList;
    }
}
