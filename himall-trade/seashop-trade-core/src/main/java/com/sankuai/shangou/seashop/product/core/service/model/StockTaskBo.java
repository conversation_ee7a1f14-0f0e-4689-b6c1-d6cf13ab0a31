package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.List;

import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:58
 */
@Setter
@Getter
public class StockTaskBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 修改类型
     */
    private StockUpdateTypeEnum updateType;

    /**
     * 修改key 支持skuId、skuAutoId、skuCode
     */
    private StockUpdateKeyEnum updateKey;

    /**
     * 批次号 1-调整库存 2-覆盖库存
     */
    private StockUpdateWayEnum updateWay;

    /**
     * 子任务集合
     */
    private List<StockTaskInfoBo> taskInfoBoList;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 业务序号
     */
    private String seqCode;

}
