package com.sankuai.shangou.seashop.user.shop.service.impl;

import java.util.Date;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopErp;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopErpRepository;
import com.sankuai.shangou.seashop.user.shop.service.ShopErpCmdService;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ErpTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopErpReq;

/**
 * @author： liweisong
 * @create： 2023/11/28 15:10
 */
@Service
public class ShopErpCmdServiceImpl implements ShopErpCmdService {

    @Resource
    private ShopErpRepository shopErpRepository;

    @Override
    @ExaminProcess(operationUserId = "operationUserId", shopId = "shopId",
        actionName = "保存店铺ERP管理", processModel = ExaminModelEnum.USER,
            processType = ExaProEnum.INSERT, repository = "shopErpRepository",
            serviceMethod = "saveShopErp", dto = SaveShopErpReq.class, entity = ShopErp.class)
    public void saveShopErp(SaveShopErpReq saveShopErpReq) {
        // 1旺店通 2聚水潭 3网店管家 4吉客云
        if(saveShopErpReq.getErpType().equals(ErpTypeEnum.BLP.getType())
            || saveShopErpReq.getErpType().equals(ErpTypeEnum.JKY.getType())){
            if(StringUtils.isEmpty(saveShopErpReq.getBlpToken())){
                throw new BusinessException("菠萝派的Token不能为空");
            }
            ShopErp blpErp = new ShopErp();
            blpErp.setShopId(saveShopErpReq.getShopId());
            blpErp.setBlpToken(saveShopErpReq.getBlpToken());
            if(shopErpRepository.flagAlreadyExistToken(blpErp)){
                throw new BusinessException("菠萝派的Token已被占用");
            }
        } else if(saveShopErpReq.getErpType().equals(ErpTypeEnum.JST.getType())){
            if (!StringUtils.isEmpty(saveShopErpReq.getJstShopId())) {
                try {
                    Integer.parseInt(saveShopErpReq.getJstShopId());
                }catch (Exception e){
                    throw new BusinessException("聚水潭的店铺编号格式错误");
                }
                ShopErp shopErpReq = new ShopErp();
                shopErpReq.setShopId(saveShopErpReq.getShopId());
                ShopErp shopErpResp = shopErpRepository.selectOne(shopErpReq);
                if (!Objects.isNull(shopErpResp) && shopErpResp.getShopId() != saveShopErpReq.getShopId()) {
                    throw new BusinessException("聚水潭的店铺编号已存在");
                }
            }
        }
        ShopErp shopErp = JsonUtil.copy(saveShopErpReq, ShopErp.class);
        shopErp.setCreateTime(new Date());
        shopErp.setUpdateTime(new Date());
        ShopErp shopErpReq = new ShopErp();
        shopErpReq.setShopId(saveShopErpReq.getShopId());
        ShopErp shopErpResp = shopErpRepository.selectOne(shopErpReq);
        boolean jstDefaultTimeFlag = !StringUtils.isEmpty(saveShopErpReq.getJstAccessToken()) && !StringUtils.isEmpty(saveShopErpReq.getJstRefreshToken());
        Date jstDate = new Date();
        if (jstDefaultTimeFlag && saveShopErpReq.getJstCodeGetTime() == null) {
            saveShopErpReq.setJstCodeGetTime(jstDate);
        }
        if (jstDefaultTimeFlag && saveShopErpReq.getJstTokenGetTime() == null) {
            saveShopErpReq.setJstTokenGetTime(jstDate);
        }
        if(Objects.isNull(shopErpResp)){
            shopErpRepository.insert(shopErp);
        } else {
            shopErpRepository.save(shopErp);
        }
    }
}
