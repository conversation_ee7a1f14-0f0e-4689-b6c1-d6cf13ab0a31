package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.FlashSaleService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@RestController
@RequestMapping("/flashSale")
public class FlashSaleQueryController implements FlashSaleQueryFeign {

    @Resource
    private FlashSaleService flashSalService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<FlashSaleSimpleResp>> pageList(@RequestBody FlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.checkParameter();
            return flashSalService.pageList(req);
        });
    }

    @PostMapping(value = "/getById", consumes = "application/json")
    @Override
    public ResultDto<FlashSaleResp> getById(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            return flashSalService.getById(req);
        });
    }

    @PostMapping(value = "/mallPageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<MallFlashSaleResp>> mallPageList(@RequestBody MallFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("mallPageList", request, req ->
                flashSalService.mallPageList(req)
        );
    }

    @PostMapping(value = "/mallAppletPageList", consumes = "application/json")
    @Override
    public ResultDto<MallAppletFlashSaleListResp> mallAppletPageList(@RequestBody MallAppletFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("mallAppletPageList", request, req ->
                flashSalService.mallAppletPageList(req)
        );
    }

    @PostMapping(value = "/componentPageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<VisualFlashSaleResp>> componentPageList(@RequestBody VisualFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("componentPageList", request, req ->
                flashSalService.componentPageList(req)
        );
    }

    @PostMapping(value = "/queryByProductId", consumes = "application/json")
    @Override
    public ResultDto<FlashSaleResp> queryByProductId(@RequestBody ProductAndShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryByProductId", request, req -> {
            req.checkParameter();
            return flashSalService.queryByProductId(req);
        });
    }

    @PostMapping(value = "/queryValidWithSkuId", consumes = "application/json")
    @Override
    public ResultDto<SkuFlashSaleDetailResp> queryValidWithSkuId(@RequestBody QuerySkuFlashSaleReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryValidWithSkuId", request, req -> {
            req.checkParameter();
            return flashSalService.queryValidWithSkuId(req);
        });
    }

    @PostMapping(value = "/queryEffectiveFlashSaleList", consumes = "application/json")
    @Override
    public ResultDto<EffectiveFlashSaleQueryListResp> queryEffectiveFlashSaleList(@RequestBody EffectiveFlashSaleQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryEffectiveFlashSaleList", request, req -> {
            return flashSalService.queryEffectiveFlashSaleList(req);
        });
    }

    @PostMapping(value = "/queryVisualFlashSaleList", consumes = "application/json")
    @Override
    public ResultDto<VisualFlashSaleListResp> queryVisualFlashSaleList(@RequestBody FlashSaleQueryByIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryVisualFlashSaleList", request, req -> {
            req.checkParameter();

            return flashSalService.queryVisualFlashSaleList(req);
        });
    }

}
