package com.sankuai.shangou.seashop.product.core.service.assist.listener.event;

import java.util.List;

import org.springframework.transaction.event.TransactionPhase;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.AbstractHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.SendSyncStockHandler;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;

import lombok.Getter;
import lombok.Setter;

/**
 * 发送异步同步库存事件
 *
 * <AUTHOR>
 * @date 2023/12/27 16:57
 */
@Setter
@Getter
public class SendSyncStockEvent extends AbstractTransactionEvent<List<StockTaskInfoBo>> {

    @Override
    public Class<? extends AbstractHandler> getHandler() {
        return SendSyncStockHandler.class;
    }

    @Override
    public TransactionPhase getTransactionPhase() {
        return TransactionPhase.AFTER_COMMIT;
    }

}
