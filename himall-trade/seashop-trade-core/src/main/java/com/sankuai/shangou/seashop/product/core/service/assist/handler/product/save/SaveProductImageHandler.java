package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.converter.SaveProductConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImage;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 保存商品图片信息
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
@Component
@Slf4j
public class SaveProductImageHandler extends AbsSaveProductHandler {

    @Resource
    private ProductImageRepository productImageRepository;
    @Resource
    private ProductAssist productAssist;

    /**
     * 创建商品图片
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void create(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        List<String> newImgList = saveProductBo.getImageList();
        Long productId = context.getProductId();

        List<ProductImage> productImageList = SaveProductConverter.convertToProductImage(newImgList, productId);
        if (!CollectionUtils.isEmpty(productImageList)) {
            productImageRepository.saveBatch(productImageList);
        }
    }

    /**
     * 更新商品图片
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void update(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo oldProductBo = context.getOldProductBo();

        List<String> newImgList = saveProductBo.getImageList();

        // 如果是编辑商品, 则需要比较图片是否发生了变动
        List<String> oldImgList = oldProductBo == null ? null : oldProductBo.getImageList();
        boolean same = productAssist.compareImgList(newImgList, oldImgList);
        if (!same) {
            context.setNeedAudit(Boolean.TRUE);
        }
    }

    /**
     * 保存到草稿
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void updateDraft(ProductContext context) {
        productImageRepository.remove(new LambdaQueryWrapper<ProductImage>().eq(ProductImage::getProductId, context.getProductId()));
        create(context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getImageList() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT_IMAGE;
    }

    @Override
    public String getHandlerName() {
        return "保存商品图片";
    }
}
