package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Schema(description = "店铺限时购配置响应体")
@Data
public class ShopFlashSaleConfigResp extends BaseThriftDto {

    /**
     * 店铺ID（平台的ID默认=0）
     */
    @Schema(description = "店铺ID")
    private Long shopId;

    /**
     * 预热时间（店铺配置）
     */
    @Schema(description = "预热时间")
    private Integer preheat;

    /**
     * 是否允许正常购买（店铺配置）
     */
    @Schema(description = "是否允许正常购买")
    private Boolean normalPurchaseFlag;


}
