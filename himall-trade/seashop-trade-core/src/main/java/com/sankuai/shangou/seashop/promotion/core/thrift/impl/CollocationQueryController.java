package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.promotion.core.service.CollocationService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.collocation.PageCollocationResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CollocationQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:22
 */
@RestController
@RequestMapping("/collocation")
public class CollocationQueryController implements CollocationQueryFeign {

    @Resource
    private CollocationService collocationService;

    @PostMapping(value = "/pageMCollocation", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PageCollocationResp>> pageMCollocation(@RequestBody PageMCollocationReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("pageMCollocation", request, req -> collocationService.pageMCollocation(req));
    }

    @PostMapping(value = "/pageSellerCollocation", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PageCollocationResp>> pageSellerCollocation(@RequestBody PageSellerCollocationReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("pageSellerCollocation", request, req -> collocationService.pageSellerCollocation(req));
    }

    @PostMapping(value = "/queryCollocationDetail", consumes = "application/json")
    @Override
    public ResultDto<CollocationResp> queryCollocationDetail(@RequestBody CollocationDetailReq request) throws TException {
        AssertUtil.throwIfNull(request.getId(), "入参ID不能为空");
        return ThriftResponseHelper.responseInvoke("queryCollocationDetail", request, req -> collocationService.queryCollocationDetail(req));
    }

    @PostMapping(value = "/queryMallCollocationList", consumes = "application/json")
    @Override
    public ResultDto<MallCollocationResp> queryMallCollocationList(@RequestBody MallCollocationReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("queryMallCollocationList", request, req -> collocationService.queryMallCollocationList(request));
    }

    @PostMapping(value = "/queryCollocationByProductIdsAndStatus", consumes = "application/json")
    @Override
    public ResultDto<List<CollocationActivityResp>> queryCollocationByProductIdsAndStatus(@RequestBody CollocationActivityReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryCollocationByProductIdsAndStatus", request, req ->
        {
            req.checkParameter();
            return collocationService.queryCollocationByProductIdsAndStatus(req);
        });
    }
}
