package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

/**
 * @author: l<PERSON><PERSON>
 * @date: 2023/11/8/008
 * @description:
 */
public enum PlatFormEnum {

    ALL(0, "平台"),
    MOBILE(4, "移动端"),
    ;

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    PlatFormEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
