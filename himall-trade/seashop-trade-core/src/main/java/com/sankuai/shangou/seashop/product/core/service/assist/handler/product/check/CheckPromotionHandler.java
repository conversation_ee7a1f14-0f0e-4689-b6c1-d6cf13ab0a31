package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuConverter;
import com.sankuai.shangou.seashop.product.core.service.hepler.CompareHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.SkuUpdateKeyEnum;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionProductService;

import lombok.extern.slf4j.Slf4j;

/**
 * 校验营销活动
 * 参与组合购、限时购的商品不能编辑商品规格且不能开启阶梯价
 *
 * <AUTHOR>
 * @date 2024/04/16 14:00
 */
@Component
@Slf4j
public class CheckPromotionHandler extends AbsProductHandler {

    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private SkuRepository skuRepository;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验营销活动, productId:{}", context.getProductId());
        if (!context.isEditFlag()) {
            log.info("【保存商品】校验营销活动,非编辑商品,不需要检验, productId:{}", context.getProductId());
            return;
        }

        List<Long> activeIdsProductIds = promotionRemoteService.collocationFlashSaleProductId(context.getShopId());
        if (CollectionUtils.isEmpty(activeIdsProductIds)) {
            log.info("【保存商品】校验营销活动, 没有查询到营销活动, productId:{}", context.getProductId());
            return;
        }

        ProductBo saveProductBo = context.getSaveProductBo();
        if (!activeIdsProductIds.contains(saveProductBo.getProductId())) {
            log.info("【保存商品】商品没有参加限时购/组合购, productId:{}", context.getProductId());
            return;
        }

        // 如果参加了组合购、限时购 则不允许开启阶梯价
        AssertUtil.throwIfTrue(saveProductBo.getWhetherOpenLadder() != null && saveProductBo.getWhetherOpenLadder(),
                "商品参与了限时购/组合购,不允许开启阶梯价");

        // 组合购/限时购都不允许下修改规格
        List<Sku> oldSkuList = skuRepository.listByProductIds(Arrays.asList(context.getProductId()));
        List<ProductSkuBo> skuBoList = saveProductBo.getSkuList();
        // 新的规格
        List<Sku> newSkuList = SkuConverter.convertToSku(skuBoList, context.getProductId(), context.getShopId());

        // 对比出规格的变动
        CompareHelper.CompareResult<Sku> compareResult =
                CompareHelper.compare(newSkuList, oldSkuList, sku -> getUniqueKey(sku, context.getSkuUpdateKey()), (oldSku, newSku) -> true);
        // 如果是部分保存 则不允许新增规格
        if (context.isPartSave()) {
            AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(compareResult.getAddList()), "商品参与了限时购/组合购,不允许新增规格");
            return;
        }
        // 全量保存 则不允许新增/删除规格
        AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(compareResult.getAddList()), "商品参与了限时购/组合购,不允许新增规格");
        AssertUtil.throwIfTrue(CollectionUtils.isNotEmpty(compareResult.getDeleteList()), "商品参与了限时购/组合购,不允许删除规格");
    }

    private static Object getUniqueKey(Sku sku, SkuUpdateKeyEnum updKey) {
        if (SkuUpdateKeyEnum.SKU_CODE.equals(updKey)) {
            return sku.getSkuCode();
        }
        if (SkuUpdateKeyEnum.SKU_AUTO_ID.equals(updKey)) {
            return sku.getId();
        }
        return sku.getSkuId();
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_PROMOTION;
    }
}
