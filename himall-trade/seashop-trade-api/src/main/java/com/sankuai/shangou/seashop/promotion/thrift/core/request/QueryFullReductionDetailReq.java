package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/04/25 16:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询满减详情入参")
public class QueryFullReductionDetailReq extends BaseParamReq {

    @Schema(description = "满减id", required = true)
    private Long id;

    @Schema(description = "店铺id")
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(id, "满减活动id不能为空");
    }


}
