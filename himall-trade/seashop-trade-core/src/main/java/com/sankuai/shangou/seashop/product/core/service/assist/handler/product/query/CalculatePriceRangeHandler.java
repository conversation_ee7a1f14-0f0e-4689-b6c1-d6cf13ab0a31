package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 计算价格区间处理去
 *
 * <AUTHOR>
 * @date 2024/03/20 13:48
 */
@Component
@Slf4j
public class CalculatePriceRangeHandler extends AbsProductHandler {

    @Resource
    private ProductAssist productAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        productBo.setMaxSalePrice(productAssist.getMaxSalePrice(productBo));

        // 计算价格区间
        productBo.setSalePriceRange(productAssist.getSalePriceRange(productBo.getMinSalePrice(), productBo.getMaxSalePrice()));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CALCULATE_PRICE_RANGE;
    }
}
