package com.sankuai.shangou.seashop.product.core.service.assist.listener.event;

import org.springframework.transaction.event.TransactionPhase;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.AbstractHandler;

import lombok.Getter;
import lombok.Setter;

/**
 * 抽象事务事件
 *
 * <AUTHOR>
 * @date 2024/01/25 11:15
 */
@Setter
@Getter
public abstract class AbstractTransactionEvent<T> {

    /**
     * 事件的具体内容
     */
    private T eventBody;

    /**
     * 该事件的处理器class
     *
     * @return
     */
    public abstract Class<? extends AbstractHandler> getHandler();

    /**
     * 指定该事件的事务阶段
     *
     * @return
     */
    public abstract TransactionPhase getTransactionPhase();

}
