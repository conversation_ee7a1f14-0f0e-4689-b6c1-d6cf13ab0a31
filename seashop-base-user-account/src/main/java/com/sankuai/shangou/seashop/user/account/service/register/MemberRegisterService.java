package com.sankuai.shangou.seashop.user.account.service.register;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.user.account.service.MemberContactService;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.thrift.account.request.BindContactCmdReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.RegisterReq;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
public abstract class MemberRegisterService {
    @Resource
    private EncryptConfig encryptConfig;
    @Resource
    private MemberRepository memberRepository;
    @Resource
    private MemberContactService memberContactService;
    private final static String DEFAULT_ENCRYPTION_MODE = "SHA-256";

    //获取注册的用户信息
    public abstract LoginResp registerMember(RegisterReq registerReq);

    //获取注册的用户信息

    Member getMember(String phone, String password){
        Member member = new Member();
        Date now = new Date();
        member.setCellPhone(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
        member.setUserName(phone);
        member.setCreateTime(now);
        member.setUpdateTime(now);
        member.setLastLoginDate(now);
        member.setEncryptionMode(DEFAULT_ENCRYPTION_MODE);
        member.setPasswordSalt(getPasswordSalt());
        member.setPassword(SecureUtil.md5(password+member.getPasswordSalt()));
        TransactionHelper.doInTransaction(()->{
            memberRepository.save(member);
            BindContactCmdReq bindContactCmdReq = new BindContactCmdReq();
            bindContactCmdReq.setContact(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
            bindContactCmdReq.setUsertype(0);
            bindContactCmdReq.setId(member.getId());
            memberContactService.changeContact(bindContactCmdReq);
        });
        return member;
    }

    public String getPasswordSalt(){
        //获取一个随机的6位数字或字母
        return RandomUtil.randomNumbers(6);
    }


}
