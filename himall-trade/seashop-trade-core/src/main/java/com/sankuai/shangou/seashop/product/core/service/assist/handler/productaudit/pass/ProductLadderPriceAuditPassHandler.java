package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPriceAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/24 16:13
 */
@Component
@Slf4j
public class ProductLadderPriceAuditPassHandler extends AbsProductAuditPassHandler {

    @Resource
    private ProductLadderPriceAuditRepository productLadderPriceAuditRepository;
    @Resource
    private ProductLadderPriceRepository productLadderPriceRepository;

    @Override
    protected void handle(ProductContext context) {
        log.info("【商品审核通过】保存阶梯价==开始, context={}", context);

        Long productId = context.getProductId();
        List<ProductLadderPriceAudit> auditList = productLadderPriceAuditRepository
                .list(new LambdaQueryWrapper<ProductLadderPriceAudit>().eq(ProductLadderPriceAudit::getProductId, productId));
        productLadderPriceRepository.remove(new LambdaQueryWrapper<ProductLadderPrice>().eq(ProductLadderPrice::getProductId, productId));
        productLadderPriceRepository.saveBatch(JsonUtil.copyList(auditList, ProductLadderPrice.class));

        log.info("【商品审核通过】保存阶梯价==结束, context={}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.PASS_PRODUCT_LADDER_PRICE_AUDIT;
    }


}
