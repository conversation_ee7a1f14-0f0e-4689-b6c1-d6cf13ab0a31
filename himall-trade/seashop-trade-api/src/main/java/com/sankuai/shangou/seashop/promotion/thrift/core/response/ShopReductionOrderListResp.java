package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ShopOrderReductionDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "可用折扣优惠返回列表")
@Data
public class ShopReductionOrderListResp extends BaseThriftDto {

    @Schema(description = "列表")
    private List<ShopOrderReductionDto> list;


}
