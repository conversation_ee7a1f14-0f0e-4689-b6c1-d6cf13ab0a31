package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.QueryFullReductionDetailReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FullReductionSimpleResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description: 提供满减活动查询功能
 */
@FeignClient(name = "himall-trade",contextId = "FullReductionQueryFeign", path = "/himall-trade/fullReduction", url = "${himall-trade.dev.url:}")
public interface FullReductionQueryFeign {

    /**
     * 优惠券活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<FullReductionSimpleResp>> pageList(@RequestBody FullReductionQueryReq request) throws TException;

    /**
     * 通过id查询优惠券信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getById", consumes = "application/json")
    ResultDto<FullReductionResp> getById(@RequestBody QueryFullReductionDetailReq request) throws TException;

    /**
     * 通过店铺id查询折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByShopId", consumes = "application/json")
    ResultDto<FullReductionResp> queryByShopId(@RequestBody ShopIdReq request) throws TException;
}
