package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ExclusivePriceProductBo extends BaseThriftDto {

    //主键ID
    private Long id;

    //活动ID
    private Long activeId;

    //商品ID
    private Long productId;

    //SkuID
    private String skuId;

    //价格
    private BigDecimal price;

    //会员id
    private Long memberId;

}
