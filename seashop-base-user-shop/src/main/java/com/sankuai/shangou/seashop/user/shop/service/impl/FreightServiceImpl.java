package com.sankuai.shangou.seashop.user.shop.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.user.account.service.assist.RegionHelper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopFreeShippingArea;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopFreeShippingAreaRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.shop.service.FreightService;
import com.sankuai.shangou.seashop.user.shop.service.assist.FreightCalculateAssistant;
import com.sankuai.shangou.seashop.user.shop.service.model.CalculateFreightBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CalculateFreightShopBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FreightServiceImpl implements FreightService {

    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ShopFreeShippingAreaRepository shopFreeShippingAreaRepository;
    @Resource
    private FreightCalculateAssistant freightCalculateAssistant;
    @Resource
    private RegionService regionService;

    /**
     * 计算运费
     * <p>运费包括店铺运费和商品运费，实际的运费取店铺运费与商品运费的最小值</p>
     * <pre>计算店铺运费
     * 1. 店铺订单金额是否满足店铺包邮条件
     * 2. 配送地址是否在店铺包邮区域
     * 3. 店铺运费规则计算店铺运费
     * </pre>
     * <p>计算商品运费，商品运费的维度是 product，店铺下单的商品信息维度是SKU；每个 product 对应一个运费模板，
     * 一个运费模板可以有多个包邮区域配置，一个包邮区域对应一个包邮配置组。
     * 商品运费计算时，要将免邮的商品拎出来，剩下的商品再根据模板配置计算运费
     * </p>
     * <pre>过滤免邮的商品
     * 1. 根据模板ID获取模板的包邮区域配置和包邮组配置
     * 2. 汇总店铺商品信息，按照模板ID分组，再按照商品ID分组，汇总商品数量和金额(两次分组是因为相关配置是根据模板来的，需要过滤和判断的是商品)
     * 3. 根据汇总的店铺商品信息遍历，判断模板匹配的包邮区域和包邮组，如果满足条件，将商品从商品列表中移除
     * 4. 结果剩下的就是还需要继续计算运费的商品
     * </pre>
     * <AUTHOR>
     * @param calculateFreightBo
     * void
     */
    @Override
    public Map<Long, BigDecimal> calculateFreight(CalculateFreightBo calculateFreightBo) {
        Map<Long, BigDecimal> shopFreightMap = new HashMap<>();
        List<CalculateFreightShopBo> shopForCalculateList = calculateFreightBo.getShopList();
        String shippingRegionPath = calculateFreightBo.getRegionPath();
        Boolean ignoreForbiddenArea = calculateFreightBo.getIgnoreForbiddenArea();
        // 获取店铺ID列表
        List<Long> shopIdList = shopForCalculateList.stream()
                .map(CalculateFreightShopBo::getShopId)
                .distinct()
                .collect(Collectors.toList());
        // 获取店铺信息
        List<Shop> shopList = shopRepository.getList(null, shopIdList);
        Map<Long, Shop> shopMap = shopList.stream().collect(Collectors.toMap(Shop::getId, Function.identity()));
        // 获取店铺包邮区域，一个店铺可以有多个包邮区域
        Map<Long, List<ShopFreeShippingArea>> shopFreeAreaMap = getShopFreeRegion(shopIdList);
        // 遍历店铺和店铺商品，计算运费
        // 每个店铺的计算互不影响，后续可以根据数据量和性能情况改成多线程
        for (CalculateFreightShopBo shopBo : calculateFreightBo.getShopList()) {
            Shop dbShop = shopMap.get(shopBo.getShopId());
            // 首先计算店铺运费，如果运费为0可以直接跳过商品运费计算
            // 虽然店铺运费为0时代表最小运费，但由于需要判断禁售区域，所以不管怎样都计算商品运费
            BigDecimal shopFreight = freightCalculateAssistant.calculateShopFreight(shippingRegionPath, shopBo, dbShop, shopFreeAreaMap.get(shopBo.getShopId()));
            // 计算商品运费，理论上商品的运费模板一定可以计算出运费
            BigDecimal productFreight = freightCalculateAssistant.calculateShopProductFreight(shippingRegionPath, shopBo, dbShop,
                    ObjectUtil.defaultIfNull(ignoreForbiddenArea, false));
            // shopFreight == null 是特殊标记，代表不满足店铺运费条件
            if (shopFreight == null && productFreight == null) {
                log.info("【运费】店铺ID={} 计算出的店铺运费为null，商品运费为null", shopBo.getShopId());
                // 如果都为null，理论上是配置有问题，抛出异常。明确指定了true才忽略
                // todo 后续放开
                /*if (!Boolean.TRUE.equals(ignoreForbiddenArea)) {
                    throw new BusinessException("运费配置异常，请联系系统管理员");
                }*/
                shopFreightMap.put(shopBo.getShopId(), BigDecimal.ZERO);
            }
            else if(shopFreight != null && productFreight != null) {
                // 取店铺运费和商品运费的最小值
                BigDecimal minFreight = shopFreight.min(productFreight);
                log.info("【运费】店铺ID={}, 计算出的店铺运费={}, 商品运费={}, 最小运费={}", shopBo.getShopId(), shopFreight, productFreight, minFreight);
                // 设置店铺运费
                shopFreightMap.put(shopBo.getShopId(), minFreight);
            }
            else if (shopFreight == null) {
                log.info("【运费】店铺ID={} 计算出的店铺运费为null，商品运费={}", shopBo.getShopId(), productFreight);
                // 设置店铺运费
                shopFreightMap.put(shopBo.getShopId(), productFreight);
            }
            else {
                log.info("【运费】店铺ID={} 计算出的店铺运费={}，商品运费为null", shopBo.getShopId(), shopFreight);
                // 设置店铺运费
                shopFreightMap.put(shopBo.getShopId(), shopFreight);
            }
        }
        return shopFreightMap;
    }

    /**
     * 店铺免邮的获取，由于区域全路径不一定有值，所以可能需要重新查询设置
     * @param shopIdList 店铺ID
     * @return 店铺免邮区域
     */
    private Map<Long, List<ShopFreeShippingArea>> getShopFreeRegion(List<Long> shopIdList) {
        List<ShopFreeShippingArea> shopFreeAreaList = shopFreeShippingAreaRepository.getByShopIdList(shopIdList);
        // 找出没有path的区域ID
        List<Integer> nonPathRegionId = shopFreeAreaList.stream()
                .filter(r -> StrUtil.isBlank(r.getRegionPath()))
                .map(ShopFreeShippingArea::getRegionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(nonPathRegionId)) {
            return shopFreeAreaList.stream()
                    .collect(Collectors.groupingBy(ShopFreeShippingArea::getShopId));
        }
        RegionIdsReq regionIdsReq = new RegionIdsReq();
        regionIdsReq.setRegionIds(nonPathRegionId);
        Map<String, AllPathRegionResp> regionRespMap = regionService.getAllPathRegions(regionIdsReq);

        return shopFreeAreaList.stream()
                .peek(r -> {
                    // 有path的不需要处理
                    if (StrUtil.isNotBlank(r.getRegionPath()) || r.getRegionId() == null) {
                        return;
                    }
                    AllPathRegionResp region = regionRespMap.get(r.getRegionId().toString());
                    // 防止配置了的地址被删除了
                    if (region != null) {
                        String path = RegionHelper.assembleRegionPath(region.getProvinceId(), region.getCityId(), region.getCountyId(), region.getTownIds());
                        r.setRegionPath(path);
                    }
                })
                // 如果配置了免邮的区域找不到基础地址，则认为不是免邮区域
                .filter(r -> StrUtil.isNotBlank(r.getRegionPath()))
                .collect(Collectors.groupingBy(ShopFreeShippingArea::getShopId));
    }

}
