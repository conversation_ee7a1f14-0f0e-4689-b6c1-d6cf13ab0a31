package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "折扣规则响应对象")
public class DiscountActiveProductDto extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "活动ID")
    private Long activeId;

    @Schema(description = "商品ID")
    @ExaminField(description = "商品ID")
    private Long productId;

    @Schema(description = "商品名称")
    @ExaminField(description = "商品名称")
    private String productName;

    @Schema(description = "商品价格(最小售价)")
    @ExaminField(description = "商品价格(最小售价)")
    private BigDecimal salePrice;


}
