package com.sankuai.shangou.seashop.seller.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/1 15:42
 */
@Data
public class ApiLogDetailReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;


    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id 不能为空");
        }
    }


}
