package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.constant.PromotionApiConstant;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "专项价查询请求对象")
public class QueryShopUserPromotionReq extends BaseParamReq {

    @Schema(description = "店铺ID", required = true)
    private List<Long> shopIdList;

    @Schema(description = "商家用户ID")
    private Long userId;

    @Override
    public void checkParameter() {
        if (CollUtil.isNotEmpty(this.shopIdList) && this.shopIdList.size() > PromotionApiConstant.IN_MAX_NUM) {
            throw new InvalidParamException("shopIdList不能超过200个");
        }
    }


}
