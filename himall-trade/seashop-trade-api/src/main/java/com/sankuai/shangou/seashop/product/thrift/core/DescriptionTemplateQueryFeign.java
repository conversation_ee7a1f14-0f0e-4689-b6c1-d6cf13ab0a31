package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.QueryDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.DescriptionTemplateListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto.DescriptionTemplateDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 版式查询服务
 *
 * author HuBiao
 * date 2023/11/20 10:17
 */
@FeignClient(name = "himall-trade", contextId = "DescriptionTemplateQueryFeign", path = "/himall-trade/descriptionTemplate", url = "${himall-trade.dev.url:}")
public interface DescriptionTemplateQueryFeign {

    /**
     * 分页查询版式列表
     */
    @PostMapping(value = "/queryDescriptionTemplateForPage", consumes = "application/json")
    ResultDto<BasePageResp<DescriptionTemplateDto>> queryDescriptionTemplateForPage(@RequestBody QueryDescriptionTemplateReq request) throws TException;

    /**
     * 查询版式详情
     */
    @PostMapping(value = "/queryDescriptionTemplateDetail", consumes = "application/json")
    ResultDto<DescriptionTemplateDetailResp> queryDescriptionTemplateDetail(@RequestBody QueryDescriptionTemplateDetailReq request) throws TException;

    /**
     * 查询版式列表(无需传分页信息)
     */
    @PostMapping(value = "/queryDescriptionTemplateForList", consumes = "application/json")
    ResultDto<DescriptionTemplateListResp> queryDescriptionTemplateForList(@RequestBody QueryDescriptionTemplateReq request) throws TException;
}
