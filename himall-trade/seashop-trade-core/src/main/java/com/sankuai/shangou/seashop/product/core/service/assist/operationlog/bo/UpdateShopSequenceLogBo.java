package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:08
 */
@Getter
@Setter
public class UpdateShopSequenceLogBo {

    @ExaminField(description = "商品列表", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateShopSequenceItemLogBo")
    private List<UpdateShopSequenceItemLogBo> productList;

    public static UpdateShopSequenceLogBo build(List<Product> productList) {
        UpdateShopSequenceLogBo bo = new UpdateShopSequenceLogBo();
        if (CollectionUtils.isEmpty(productList)) {
            return bo;
        }

        bo.setProductList(JsonUtil.copyList(productList, UpdateShopSequenceItemLogBo.class));
        return bo;
    }

    public static UpdateShopSequenceLogBo build(List<Long> productIdList, Integer displaySequence) {
        UpdateShopSequenceLogBo bo = new UpdateShopSequenceLogBo();
        if (CollectionUtils.isEmpty(productIdList)) {
            return bo;
        }

        List<UpdateShopSequenceItemLogBo> productList = new ArrayList<>();
        for (Long productId : productIdList) {
            UpdateShopSequenceItemLogBo itemBo = new UpdateShopSequenceItemLogBo();
            itemBo.setProductId(productId);
            itemBo.setShopDisplaySequence(displaySequence);
            productList.add(itemBo);
        }
        bo.setProductList(productList);
        return bo;
    }

}
