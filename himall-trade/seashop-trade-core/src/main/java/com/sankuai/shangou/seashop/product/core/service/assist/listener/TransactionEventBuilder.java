package com.sankuai.shangou.seashop.product.core.service.assist.listener;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.AbstractTransactionEvent;

/**
 * <AUTHOR>
 * @date 2024/01/25 14:33
 */
public class TransactionEventBuilder {

    public static <T> AbstractTransactionEvent<T> of(Class<? extends AbstractTransactionEvent> clazz, T eventBody) {
        try {
            AbstractTransactionEvent<T> event = clazz.newInstance();
            event.setEventBody(eventBody);
            return event;
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
