package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:25
 */
@Data
@Schema(description = "商品详情-组合购活动分组入参")
public class MallCollocationReq extends BaseParamReq {

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "店铺ID")
    private Long shopId;


    public void checkParameter() {
        AssertUtil.throwIfNull(productId, "productId不能为空");
        AssertUtil.throwIfNull(shopId, "shopId不能为空");
    }
}
