package com.sankuai.shangou.seashop.base.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.dto.BaseOperationLogDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.BaseOperationLogService;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Component
//@MafkaConsumer(namespace = "waimai", topic = "seashop_operation_log_topic", group = "seashop_operation_log_consumer")
@RocketMQMessageListener( topic = "seashop_operation_log_topic" + "_${spring.profiles.active}"
        , consumerGroup = "seashop_operation_log_consumer"+ "_${spring.profiles.active}", selectorExpression = "*")
public class LogMafkaConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private BaseOperationLogService operationLogService;

    @Override
    public void onMessage(MessageExt message) {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("receive message: {}", body);
            if (body == null) {
                return;
            }

            BaseOperationLogDto operationLog = JsonUtil.parseObject(body, BaseOperationLogDto.class);
            operationLogService.insert(operationLog);

        }
        catch (Exception e) {
            log.error("log consumer fail,", e);
            throw new RuntimeException(e);
        }
    }
}