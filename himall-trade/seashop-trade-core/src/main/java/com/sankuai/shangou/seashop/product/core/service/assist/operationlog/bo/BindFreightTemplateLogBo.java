package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 16:16
 */
@Getter
@Setter
public class BindFreightTemplateLogBo {

    @ExaminField(description = "商品id的集合", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.BindFreightTemplateItemLogBo")
    private List<BindFreightTemplateItemLogBo> productList;

    public static BindFreightTemplateLogBo build(List<Long> productIdList, Long freightTemplateId) {
        BindFreightTemplateLogBo bo = new BindFreightTemplateLogBo();
        if (CollectionUtils.isEmpty(productIdList)) {
            return bo;
        }

        List<BindFreightTemplateItemLogBo> productList = new ArrayList<>();
        for (Long productId : productIdList) {
            BindFreightTemplateItemLogBo itemBo = new BindFreightTemplateItemLogBo();
            itemBo.setProductId(productId);
            itemBo.setFreightTemplateId(freightTemplateId);
            productList.add(itemBo);
        }
        bo.setProductList(productList);
        return bo;
    }

    public static BindFreightTemplateLogBo build(List<Product> productList) {
        BindFreightTemplateLogBo bo = new BindFreightTemplateLogBo();
        bo.setProductList(JsonUtil.copyList(productList, BindFreightTemplateItemLogBo.class));
        return bo;
    }

}
