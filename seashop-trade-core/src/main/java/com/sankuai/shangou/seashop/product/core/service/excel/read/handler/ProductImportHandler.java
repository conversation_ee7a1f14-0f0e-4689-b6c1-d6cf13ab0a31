package com.sankuai.shangou.seashop.product.core.service.excel.read.handler;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.base.thrift.core.TopicQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TemplateClientTypeEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteFreightAreaService;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.core.service.*;
import com.sankuai.shangou.seashop.product.core.service.assist.BizCodeAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.sku.SkuCombinationAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.sku.SpecCombinationBo;
import com.sankuai.shangou.seashop.product.core.service.excel.read.BizTypeEnum;
import com.sankuai.shangou.seashop.product.core.service.excel.read.context.ProductImportAssist;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductImportDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ProductImportNewDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.dto.ThirdApiOeDto;
import com.sankuai.shangou.seashop.product.core.service.excel.read.wrapper.ProductImportWrapper;
import com.sankuai.shangou.seashop.product.core.service.model.*;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.*;
import com.sankuai.shangou.seashop.product.dao.core.model.SpecValueDto;
import com.sankuai.shangou.seashop.product.dao.core.repository.*;
import com.sankuai.shangou.seashop.product.thrift.core.constant.ParameterConstant;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;
import com.sankuai.shangou.seashop.product.thrift.core.enums.*;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;
import com.sankuai.shangou.seashop.product.thrift.core.helper.ParameterHelper;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductSkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.ProductFieldDto;
import com.sankuai.shangou.seashop.user.thrift.shop.FreightAreaQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品导入处理器
 *
 * <AUTHOR>
 * @date 2023/11/21 22:08
 */
@Configuration
@Slf4j
@SuppressWarnings("all")
public class ProductImportHandler extends ImportHandler<ProductImportNewDto> {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private CategoryRepository categoryRepository;
    @Resource
    private BrandRepository brandRepository;
    @Resource
    private BizCodeAssist bizCodeAssist;
    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private ShopCategoryRepository shopCategoryRepository;
    @Resource
    private SkuAssist skuAssist;
    @Resource
    private ProductService productService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private SkuCombinationAssist skuCombinationAssist;
    @Resource
    private SkuService skuService;
    @Resource
    private SkuStockService skuStockService;
    @Resource
    private ProductAssist productAssist;
    @Resource
    private RemoteFreightAreaService remoteFreightAreaService;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private SpecNameRepository specNameRepository;
    @Resource
    private SpecValueRepository specValueRepository;
    @Resource
    private TopicQueryFeign topicQueryFeign;
    @Resource
    private CategoryService categoryService;
    @Resource
    private BrandService brandService;
    @Resource
    private ShopCategoryService shopCategoryService;
    @Value("${chengpei.api.url}")
    private String chengpeiApiUrl;
    @Resource
    private FreightAreaQueryFeign freightAreaQueryFeign;

    @Override
    public BizType bizType() {
        return BizTypeEnum.PRODUCT_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<ProductImportNewDto> importResult) {
        Long shopId = ProductImportAssist.getShopIdOrThrow();
        RemoteShopBo shop = remoteShopService.checkAndGetShop(shopId);
        List<ProductFieldDto> fields = ProductImportAssist.getFields();
        // 获取关联数据对应下标的Map
        Map<String, Integer> fieldIndexMap = this.getFieldIndexMap(fields);
        // 关联Map, key为关联字段名称，value为表头名称
        Map<String, String> fieldMap = Maps.newHashMap();
        for(ProductFieldDto field : fields) {
            if (StringUtils.isNotEmpty(field.getFieldName())) {
                fieldMap.put(field.getFieldName(), field.getName());
            }
        }
        // 获取数据格式校验通过的数据
        List<ProductImportNewDto> productList = importResult.getSuccessDataList();

        BaseWapTopicQueryReq query = new BaseWapTopicQueryReq();
        query.setType(TemplateClientTypeEnum.Header.getCode());
        query.setClient("header");
        query.setShopId(0L);
        BaseWapTopicRes result = ThriftResponseHelper.executeThriftCall(() ->
                topicQueryFeign.getWapTopicById(query));
        log.info("导入商品请求logo:" + result.getPage());
        JSONObject pageJson = JSONObject.parseObject(result.getPage());
        String loge = pageJson.getString("logo");
        // 默认分类
        CategoryQueryBo queryBo = new CategoryQueryBo();
        queryBo.setNameLike("默认分类");
        queryBo.setDepth(1);
        CategoryBo oneCategory = categoryService.queryCategoryByNameDepth(queryBo);
        if(oneCategory == null) {
            throw new BusinessException("一级分类：【默认分类】，不存在");
        }
        queryBo.setDepth(2);
        CategoryBo twoCategory = categoryService.queryCategoryByNameDepth(queryBo);
        if(twoCategory == null) {
            throw new BusinessException("二级分类：【默认分类】，不存在");
        }
        queryBo.setDepth(3);
        CategoryBo threeCategory = categoryService.queryCategoryByNameDepth(queryBo);
        if(threeCategory == null) {
            throw new BusinessException("三级分类：【默认分类】，不存在");
        }
        // 店铺分类
        ShopCategoryQueryBo queryShopBo = ShopCategoryQueryBo.builder().build();
        queryShopBo.setName("默认分类");
        queryShopBo.setShopId(shopId);
        List<ShopCategory> scList = shopCategoryService.queryByShopIdCategoryName(queryShopBo);
        ShopCategory oneShopCategory = null;
        ShopCategory twoShopCategory = null;
        for(ShopCategory sc :scList) {
            if(sc.getParentCategoryId() == 0) {
                oneShopCategory = sc;
            }
        }
        for(ShopCategory sc :scList) {
            if(oneShopCategory.getId().equals(sc.getParentCategoryId())) {
                twoShopCategory = sc;
            }
        }
        // 默认品牌
        Brand defaultBrand = brandService.queryByName("默认品牌");;
        // 查询默认运费模版
        QueryFreightTemplateReq req1 = new QueryFreightTemplateReq();
        req1.setShopId(shopId);
        QueryFreightTemplateResp result1 = ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryFreightTemplateList(req1));
        List<QueryFreightTemplateDto> flist = result1.getResult();
        this.checkProductAndBuildProductBo(productList, shopId, fieldIndexMap, fieldMap, defaultBrand, loge, oneCategory,
                 twoCategory,
                 threeCategory,
                 oneShopCategory,
                 twoShopCategory,
                 flist.get(0).getId());
        buildErrMsg(productList);
    }

    /**
     * 获取关联数据对应下标的Map
     * @param fields
     * @return
     */
    private Map<String, Integer> getFieldIndexMap(List<ProductFieldDto> fields) {
        Map<String, Integer> fieldIndexMap = Maps.newHashMap();
        fieldIndexMap.put("*商品名称", -1);
        fieldIndexMap.put("*OE号", -1);
        fieldIndexMap.put("*库存", -1);
        fieldIndexMap.put("*商城价", -1);
        fieldIndexMap.put("品牌号", -1);
        fieldIndexMap.put("品牌", -1);
        fieldIndexMap.put("适用车型", -1);
        // 只保存不为空的字段
        int index = 0;
        for(ProductFieldDto field : fields) {
            if(StringUtils.isNotEmpty(field.getFieldName())) {
                fieldIndexMap.put(field.getFieldName(), index);
            }
            index++;
        }
        return fieldIndexMap;
    }

    @Override
    public void saveImportData(List<ProductImportNewDto> successList) {
        Integer saleStatus = ProductImportAssist.getSaleStatus();
        for(ProductImportNewDto product : successList) {
            try {
                ProductBo productBo = product.getProductBo();
                // 设置销售状态
                productBo.setSaleStatus(saleStatus);
                if(product.getIzUpdate()) {
                    ProductSkuQueryReq queryBo = ProductSkuQueryReq.builder()
                            .shopId(product.getShopId())
                            .productIds(Lists.newArrayList(product.getProductBo().getProductId()))
                            .build();
                    List<Sku> skus = skuService.querySkuList(queryBo);
                    StockTaskBo taskBo = new StockTaskBo();
                    List<StockTaskInfoBo> taskInfoBoList = Lists.newArrayList();
                    for(Sku sku : skus) {
                        // 更新库存
                        Long shopId = ProductImportAssist.getShopIdOrThrow();
                        taskBo.setBizCode(IdUtil.fastUUID());
                        taskBo.setUpdateType(StockUpdateTypeEnum.EXCEL_IMPORT_UPDATE);
                        taskBo.setUpdateWay(StockUpdateWayEnum.COVER);
                        taskBo.setUpdateKey(StockUpdateKeyEnum.SKU_AUTO_ID);
                        StockTaskInfoBo taskInfoBo = new StockTaskInfoBo();
                        taskInfoBo.setShopId(shopId);
                        taskInfoBo.setSkuAutoId(sku.getId());
                        taskInfoBo.setStock(product.getProductBo().getStock());
                        taskInfoBo.setSkuCode(product.getProductBo().getProductCode());
                        taskInfoBoList.add(taskInfoBo);
                    }
                    taskBo.setTaskInfoBoList(taskInfoBoList);
                    skuStockService.asyncChangeSkuStock(taskBo);
                } else {
                    // 保存商品
                    productService.saveProduct(productBo, ProductSourceEnum.MALL, ProductChangeType.IMPORT_CREATE, false, false);
                }
            } catch (Exception e) {
                product.setErrMsg(String.format("导入失败: 【%s】", e.getMessage()));
                log.error("商品导入失败: product: {}", product, e);
            }
        }
    }

    @Override
    public DataWrapper<ProductImportNewDto> wrapData(List<ProductImportNewDto> errList) {
        return new ProductImportWrapper(errList);
    }

    /**
     * 自动生成货号
     *
     * @param productList
     */
    private List<String> genProductCode(List<ProductImportNewDto> successList) {
        if (CollectionUtils.isEmpty(successList)) {
            return Lists.newArrayList();
        }
        List<String> productCodes = bizCodeAssist.getProductCodes(successList.size());
        return productCodes;
    }

    /**
     * 校验商品信息
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkProductAndBuildProductBo(List<ProductImportNewDto> productList, Long shopId,
                              Map<String, Integer> fieldIndexMap,
                              Map<String, String> fieldMap,
                              Brand defaultBrand,
                              String imagePath,
                              CategoryBo oneCategory,
                              CategoryBo twoCategory,
                              CategoryBo threeCategory,
                              ShopCategory oneShopCategory,
                              ShopCategory twoShopCategory,
                              Long freightTemplateId
                              ) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        List<ProductImportNewDto> needCodeList = Lists.newArrayList();
        productList.forEach(product -> {
            // *商品名称
            String productName = getFieldValue(product, fieldIndexMap, "*商品名称");
            if (StringUtils.isEmpty(productName)) {
                product.getErrBuilder().append("商品名称不能为空;");
            }
            if (StringUtils.isNotEmpty(productName) && productName.length() > ParameterConstant.PRODUCT_NAME_LENGTH) {
                product.getErrBuilder().append("商品名称长度不能超过").append(ParameterConstant.PRODUCT_NAME_LENGTH).append("个字符;");
            }
            // *OE号 (商品编码)
            String oeNumber = getFieldValue(product, fieldIndexMap, "*OE号");
            if (StringUtils.isEmpty(oeNumber)) {
                product.getErrBuilder().append("OE号不能为空;");
            }
            // *库存
            String stock = getFieldValue(product, fieldIndexMap, "*库存");
            if (stock != null) {
                stock = stock.replaceAll(",", "");
            }
            this.checkStock(product, stock);

            // *商城价
            String salePrice = getFieldValue(product, fieldIndexMap, "*商城价");
            // 价格有可能是待,的，全部给替换掉
            if(salePrice != null) {
                salePrice = salePrice.replaceAll(",", "");
            }
            this.checkPrice(product, salePrice);

            // 品牌
            String brandName = getFieldValue(product, fieldIndexMap, "品牌");
            // 有的情况下面才去校验
            if (StringUtils.isNotEmpty(brandName)) {
                Brand brand = brandService.queryByName(brandName);
                if(brand == null) {
                    //product.getErrBuilder().append("品牌【"+brandName+"】不存在;");
                    brand = defaultBrand;
                }
            }
            // 没有报错就赋值
            if(StringUtils.isEmpty(product.getErrBuilder().toString())) {
                // 根据实际情况，重新构建商品对象
                ProductBo productBo = productBoBuild(product, fieldIndexMap, defaultBrand);
                product.setShopId(shopId);
                // 商品主图
                productBo.setImagePath(imagePath);
                productBo.setImageList(Arrays.asList(imagePath));
                // 根据 OE号 + 品牌号 + 品牌id 查询商品，存咋则更新库存，不存在，则调用客户的商品接口
                Product old = productService.queryProduct(productBo.getOeCode(), productBo.getBrandCode(), productBo.getBrandId(), shopId);
                if(old != null) {
                    productBo.setProductId(old.getProductId());
                    // 打标
                    product.setIzUpdate(Boolean.TRUE);
                    product.setProductBo(productBo);
                } else {
                    // 调用OE零件数据清洗接口
                    ThirdApiOeDto dto = callOeDataCleaningApi(productBo.getOeCode());
                    if(StringUtils.isNotEmpty(dto.getErrMsg())) {
                        // 报错接口调用的报错信息
                        product.getErrBuilder().append(dto.getErrMsg() + ";");
                    } else {
                        productBo.setCategoryId(threeCategory.getId());
                        productBo.setCategoryPath(oneCategory.getId() + "|"  + twoCategory.getId() + "|" + threeCategory.getId());
                        List<Long> shopCategoryIdList = Lists.newArrayList();
                        shopCategoryIdList.add(twoShopCategory.getId());
                        productBo.setShopCategoryIdList(shopCategoryIdList);
                        productBo.setProductName(dto.getPartsName());
                        productBo.setAdaptableCar(dto.getCars());
                        productBo.setPartSpec(dto.getPartsInfo());
                        productBo.setReplaceNumber(dto.getReplaceNo());
                        productBo.setShopId(shopId);
                        productBo.setMeasureUnit("件");
                        productBo.setHasSku(Boolean.FALSE);
                        productBo.setStock(Long.parseLong(stock));
                        productBo.setPartQuality(4);
                        productBo.setMinSalePrice(new BigDecimal(salePrice));
                        productBo.setMarketPrice(new BigDecimal(salePrice));
                        productBo.setFreightTemplateId(freightTemplateId);
                        // 组装一个默认的sku
                        ProductSkuBo skuBo = new ProductSkuBo();
                        skuBo.setSkuId("0_0_0_0");
                        skuBo.setSalePrice(new BigDecimal(salePrice));
                        skuBo.setStock(Long.parseLong(stock));
                        skuBo.setSkuCode("");
                        skuBo.setMeasureUnit("件");
                        productBo.setSkuList(Arrays.asList(skuBo));
                        product.setIzUpdate(Boolean.FALSE);
                        product.setProductBo(productBo);
                        needCodeList.add(product);
                    }
                }
            }
        });
        // 自动生成商品编号
        List<String> codeList = this.genProductCode(needCodeList);
        // 给需要商品号的商品设置商品编号
        int index = 0;
        for(ProductImportNewDto item : needCodeList) {
            if(item.getProductBo() != null && !item.getIzUpdate()) {
                item.getProductBo().setProductCode(codeList.get(index));
                item.getProductBo().getSkuList().forEach(sku -> {
                    sku.setSkuCode(item.getProductBo().getProductCode());
                });
            }
            index++;
        }
    }

    private void checkProductImage(List<ProductImportDto> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        Map<String, String> remoteImgMapping = ProductImportAssist.getRemoteImgMapping();

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getImagePath())) {
                return;
            }

            List<String> localImageList = Arrays.asList(product.getImagePath().split(StrUtil.COMMA));
            List<String> imageList = new ArrayList<>();
            // 将本地图片转换为
            localImageList.forEach(localImage -> {
                String remoteImage = remoteImgMapping.get(localImage);
                if (StringUtils.isEmpty(remoteImage)) {
                    product.getErrBuilder().append(String.format("[%s]图片不存在;", localImage));
                    return;
                }

                imageList.add(remoteImage);
            });

            if (product.getErrBuilder().length() == 0) {
                product.setImageList(imageList);
                product.setImagePath(CollectionUtils.isNotEmpty(imageList) ? imageList.get(0) : null);
            }
        });
    }

    /**
     * 校验商品分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkCategory(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        checkFirstCategory(productList, shopId);
        checkSecondCategory(productList, shopId);
        checkThirdCategory(productList, shopId);
    }

    /**
     * 校验一级分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkFirstCategory(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> firstCategoryNames = productList.stream()
                .map(ProductImportDto::getFirstCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<Category> categoryList = categoryRepository.listCategoryByPidAndName(CommonConstant.DEFAULT_PARENT_ID, firstCategoryNames);
        Map<String, Category> categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getName, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getFirstCategoryName())) {
                product.getErrBuilder().append("一级分类不能为空;");
                return;
            }

            Category category = categoryMap.get(product.getFirstCategoryName());
            if (category == null) {
                product.getErrBuilder().append("一级分类不存在;");
                return;
            }

            product.setFirstCategoryId(category.getId());
        });
    }

    /**
     * 校验二级分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkSecondCategory(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<Long> firstCategoryIds = productList.stream()
                .map(ProductImportDto::getFirstCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Category> categoryList = categoryRepository.listByParentIds(firstCategoryIds);
        Map<Long, Map<String, Category>> categoryMap = categoryList.stream()
                .collect(Collectors.groupingBy(Category::getParentCategoryId, Collectors.toMap(Category::getName, Function.identity(), (k1, k2) -> k2)));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getSecondCategoryName())) {
                product.getErrBuilder().append("二级分类不能为空;");
                return;
            }

            if (product.getFirstCategoryId() == null) {
                return;
            }

            Map<String, Category> subCategoryMap = categoryMap.get(product.getFirstCategoryId());
            if (MapUtils.isEmpty(subCategoryMap)) {
                product.getErrBuilder().append("二级分类不存在;");
                return;
            }

            Category category = subCategoryMap.get(product.getSecondCategoryName());
            if (category == null) {
                product.getErrBuilder().append("二级分类不存在;");
                return;
            }

            product.setSecondCategoryId(category.getId());
        });
    }

    /**
     * 校验三级分类
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkThirdCategory(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<Long> secondCategoryIds =
                productList.stream().map(ProductImportDto::getSecondCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Category> categoryList = categoryRepository.listByParentIds(secondCategoryIds);
        Map<Long, Map<String, Category>> categoryMap = categoryList.stream()
                .collect(Collectors.groupingBy(Category::getParentCategoryId, Collectors.toMap(Category::getName, Function.identity(), (k1, k2) -> k2)));

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getThirdCategoryName())) {
                product.getErrBuilder().append("三级分类不能为空;");
                return;
            }

            if (product.getSecondCategoryId() == null) {
                return;
            }

            Map<String, Category> subCategoryMap = categoryMap.get(product.getSecondCategoryId());
            if (MapUtils.isEmpty(subCategoryMap)) {
                product.getErrBuilder().append("三级分类不存在;");
                return;
            }

            Category category = subCategoryMap.get(product.getThirdCategoryName());
            if (category == null) {
                product.getErrBuilder().append("三级分类不存在;");
                return;
            }

            product.setThirdCategoryId(category.getId());
            product.setCategoryPath(category.getPath());
        });
    }

    /**
     * 校验品牌
     *
     * @param productList 导入的数据
     * @param shop        店铺
     */
    private void checkBrand(List<ProductImportDto> productList, RemoteShopBo shop) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> brandNames = productList.stream()
                .map(ProductImportDto::getBrandName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        List<Brand> brandList = brandRepository.getByBrandNames(brandNames);
        Map<String, Brand> brandMap = brandList.stream().collect(Collectors.toMap(Brand::getName, Function.identity(), (k1, k2) -> k2));
        List<Long> authBrandIds = shopBrandRepository.listBrandIdsByShopId(shop.getId());

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getBrandName())) {
                product.getErrBuilder().append("品牌不能为空;");
                return;
            }

            Brand brand = brandMap.get(product.getBrandName());
            if (brand == null) {
                product.getErrBuilder().append("品牌不存在;");
                return;
            }

            if (!authBrandIds.contains(brand.getId()) && !shop.getWhetherSelf()) {
                product.getErrBuilder().append("该供应商不支持此品牌;");
                return;
            }

            product.setBrandId(brand.getId());
        });
    }

    /**
     * 校验店铺品牌
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkShopCategory(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> shopCategoryNames = productList.stream()
                .map(ProductImportDto::getShopCategoryName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        List<ShopCategory> shopCategories = shopCategoryRepository.listShopCategory(shopCategoryNames, shopId);
        Map<String, ShopCategory> shopCategoryMap = shopCategories.stream().collect(Collectors.toMap(ShopCategory::getName, Function.identity(), (k1, k2) -> k2));
        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getShopCategoryName())) {
                product.getErrBuilder().append("店铺分类不能为空;");
                return;
            }

            ShopCategory shopCategory = shopCategoryMap.get(product.getShopCategoryName());
            if (shopCategory == null) {
                product.getErrBuilder().append("店铺分类不存在;");
                return;
            }

            product.setShopCategoryId(shopCategory.getId());
        });
    }

    /**
     * 校验运费模板
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkFreightTemplate(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        List<String> freightTemplateNames = productList.stream()
                .map(ProductImportDto::getFreightTemplateName).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<QueryFreightTemplateDto> templateList = remoteFreightAreaService.queryFreightTemplate(shopId, freightTemplateNames);
        Map<String, QueryFreightTemplateDto> templateMap = templateList.stream().collect(Collectors.toMap(QueryFreightTemplateDto::getName, Function.identity(), (k1, k2) -> k2));

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getFreightTemplateName())) {
                product.getErrBuilder().append("运费模板不能为空;");
                return;
            }

            // 重量和体积只能为数字
            if (StringUtils.isNotEmpty(product.getWeight()) && !NumberUtil.isNumber(product.getWeight())) {
                product.getErrBuilder().append("重量格式不正确(只能为数字);");
                return;
            }

            if (StringUtils.isNotEmpty(product.getVolume()) && !NumberUtil.isNumber(product.getVolume())) {
                product.getErrBuilder().append("体积格式不正确(只能为数字);");
                return;
            }

            QueryFreightTemplateDto freightTemplate = templateMap.get(product.getFreightTemplateName());
            if (freightTemplate == null) {
                product.getErrBuilder().append("运费模板不存在;");
                return;
            }

            TemplateValuationMethod valuationMethod = TemplateValuationMethod.getByCode(freightTemplate.getValuationMethod());
            switch (valuationMethod) {
                case WEIGHT:
                    // 如果是根据重量计费 则重量必填
                    if (StringUtils.isEmpty(product.getWeight())) {
                        product.getErrBuilder().append("重量不能为空;");
                    }
                    product.setVolume(null);
                    break;
                case VOLUME:
                    // 体积不能为空
                    if (StringUtils.isEmpty(product.getVolume())) {
                        product.getErrBuilder().append("体积不能为空;");
                    }
                    product.setWeight(null);
                    break;
                default:
                    product.setWeight(null);
                    product.setVolume(null);
                    break;
            }

            product.setFreightTemplateId(freightTemplate.getId());
        });
    }

    /**
     * 校验库存
     *
     * @param productList
     */
    private void checkStock(ProductImportNewDto product, String stock) {
            if (StringUtils.isEmpty(stock)) {
                product.getErrBuilder().append("库存不能为空;");
                return;
            }

            if (!NumberUtil.isLong(stock)) {
                product.getErrBuilder().append("库存格式异常, 请输入正整数");
                return;
            }
            if (!ParameterHelper.checkStock(Long.parseLong(stock))) {
                product.getErrBuilder().append(String.format("库存取值范围为%s-%s;", ParameterConstant.MIN_STOCK, ParameterConstant.MAX_STOCK));
            }
    }

    /**
     * 校验价格
     *
     * @param productList
     */
    private void checkPrice(ProductImportNewDto product, String price) {
        if (StringUtils.isEmpty(price)) {
            product.getErrBuilder().append("商城价不能为空;");
        }

        if (StringUtils.isNotEmpty(price)) {
            // 商城价格
            if (!ParameterHelper.checkAmount(price)) {
                product.getErrBuilder().append(String.format("商城价范围为%s-%s, 且最多保留两位小数", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
            }

            // 商城价范围
            else if (!ParameterHelper.checkPrice(BigDecimal.valueOf(Double.parseDouble(price)))) {
                product.getErrBuilder().append(String.format("商城价范围为%s-%s;", ParameterConstant.MIN_PRICE, ParameterConstant.MAX_PRICE));
            }
        }
    }

    /**
     * 校验sku
     *
     * @param productList 导入的数据
     * @param shopId      店铺id
     */
    private void checkSku(List<ProductImportDto> productList, Long shopId) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> {
            if (StringUtils.isEmpty(product.getSpecs())) {
                product.setHasSku(false);
                product.setSkuList(Arrays.asList(buildSingleSpec(product)));
                return;
            }

            // 拆分后的规格map
            Map<String, List<String>> specMap = skuAssist.splitSpecStr(product.getSpecs());
            if (MapUtils.isEmpty(specMap)) {
                product.getErrBuilder().append("规格不能为空;");
                return;
            }

            List<ProductSkuBo> skuList = new ArrayList<>();
            List<ProductSkuBo> tempSkuList = new ArrayList<>();
            Set<String> specNames = specMap.keySet();
            int level = 0;
            for (String specName : specNames) {
                level++;
                tempSkuList = new ArrayList<>(skuList);
                skuList.clear();

                List<String> specValues = specMap.get(specName);
                if (CollectionUtils.isEmpty(specValues)) {
                    product.getErrBuilder().append("规格值不能为空;");
                    return;
                }

                SpecName specNameEntity = specNameRepository.getByName(shopId, specName);
                if (specNameEntity == null) {
                    product.getErrBuilder().append(String.format("[%s]规格不存在", specName));
                    continue;
                }

                // 查询规格值
                SpecValueDto valueParam = new SpecValueDto();
                valueParam.setShopId(shopId);
                valueParam.setNameId(specNameEntity.getId());
                valueParam.setValues(specValues);
                List<SpecValue> specValueEntityList = specValueRepository.getByCondition(valueParam);
                Map<String, SpecValue> specValueEntityMap = specValueEntityList.stream()
                        .collect(Collectors.toMap(SpecValue::getValue, Function.identity(), (k1, k2) -> k1));

                for (int i = 0; i < specValues.size(); i++) {
                    String specValue = specValues.get(i);
                    SpecValue specValueEntity = specValueEntityMap.get(specValue);
                    if (specValueEntity == null) {
                        product.getErrBuilder().append(String.format("[%s]规格值不存在", specValue));
                        continue;
                    }

                    SpecDto specDto = new SpecDto();
                    specDto.setNameId(specNameEntity.getId());
                    specDto.setSpecName(specNameEntity.getSpecName());
                    specDto.setSpecAlias(specNameEntity.getSpecAlias());
                    specDto.setValueId(specValueEntity.getId());
                    specDto.setSpecValue(specValueEntity.getValue());

                    // 如果是第一层规格, 直接往skuList 里面加即可
                    if (level == 1) {
                        ProductSkuBo sku = new ProductSkuBo();
                        sku.setSpecList(Arrays.asList(specDto));
                        // 第一层的时候 需要加入售价和库存
                        if (NumberUtil.isNumber(product.getSalePrice())) {
                            sku.setSalePrice(BigDecimal.valueOf(Double.parseDouble(product.getSalePrice())));
                        }
                        if (NumberUtil.isLong(product.getStock())) {
                            sku.setStock(Long.parseLong(product.getStock()));
                        }
                        skuList.add(sku);
                    }
                    // 规格层数大于1, 需要将规格值组合
                    else {
                        for (int j = 0; j < tempSkuList.size(); j++) {
                            ProductSkuBo sku = JsonUtil.parseObject(JsonUtil.toJsonString(tempSkuList.get(j)), ProductSkuBo.class);
                            sku.getSpecList().add(specDto);
                            skuList.add(sku);
                        }
                    }
                }
            }


            // 指定货号
            for (int i = 0; i < skuList.size(); i++) {
                ProductSkuBo sku = skuList.get(i);
                sku.setSkuCode(product.getProductCode() + CommonConstant.SKU_CODE_SPLIT + i);
            }

            product.setSkuList(skuList);
            product.setHasSku(true);
        });
    }

    /**
     * 构建保存商品参数
     *
     * @param productImportDto 导入的商品数据
     * @param fieldIndexMap 字段索引映射
     * @return 商品参数
     */
    private ProductBo productBoBuild(ProductImportNewDto productImportDto, Map<String, Integer> fieldIndexMap, Brand defaultBrand) {
        Long operationUserId = ProductImportAssist.getOperationUserId();
        Long operationShopId = ProductImportAssist.getOperationShopId();

        ProductBo product = new ProductBo();

        // 根据fieldIndexMap动态设置商品属性
        // *商品名称
        String productName = getFieldValue(productImportDto, fieldIndexMap, "*商品名称");
        if (StringUtils.isNotEmpty(productName)) {
            product.setProductName(productName);
        }

        // *OE号 (商品编码)
        String oeNumber = getFieldValue(productImportDto, fieldIndexMap, "*OE号").trim().toUpperCase();
        // oeNumber去掉中间的空格
        oeNumber = oeNumber.replaceAll("\\s+", "");
        product.setOeCode(oeNumber);

        // *库存
        String stock = getFieldValue(productImportDto, fieldIndexMap, "*库存");
        if (StringUtils.isNotEmpty(stock)) {
            stock = stock.replaceAll(",", "");
        }
        product.setStock(Long.valueOf(stock));

        // *商城价
        String salePrice = getFieldValue(productImportDto, fieldIndexMap, "*商城价");
        if (StringUtils.isNotEmpty(salePrice)) {
            salePrice = salePrice.replaceAll(",", "");
            product.setMinSalePrice(new BigDecimal(salePrice));
        }

        // 品牌号
        String brandCode = getFieldValue(productImportDto, fieldIndexMap, "品牌号");
        if (StringUtils.isNotEmpty(brandCode)) {
            // 这里可以根据品牌号查询品牌ID，暂时先设置品牌名称
            product.setBrandCode(brandCode);
        } else {
            product.setBrandCode(oeNumber);
        }

        // 品牌
        String brandName = getFieldValue(productImportDto, fieldIndexMap, "品牌");
        if (StringUtils.isNotEmpty(brandName)) {
            Brand brand = brandService.queryByName(brandName);
            if(brand != null) {
                product.setBrandId(brand.getId());
                product.setBrandName(brand.getName());
            } else {
                product.setBrandId(defaultBrand.getId());
                product.setBrandName(defaultBrand.getName());
            }
        } else {
            product.setBrandId(defaultBrand.getId());
            product.setBrandName(defaultBrand.getName());
        }

        // 适用车型
        String applicableModel = getFieldValue(productImportDto, fieldIndexMap, "适用车型");
        if (StringUtils.isNotEmpty(applicableModel)) {
            // 可以将适用车型信息存储到商品描述或其他字段中
            product.setMobileDescription(applicableModel);
        }

        // 导入指定销售状态
        product.setSaleStatus(ProductImportAssist.getSaleStatus());
        product.setOperationUserId(operationUserId);
        product.setOperationShopId(operationShopId);
        return product;
    }

    /**
     * 根据字段名称和索引映射获取对应的字段值
     *
     * @param productImportDto 导入的商品数据
     * @param fieldIndexMap 字段索引映射
     * @param fieldName 字段名称
     * @return 字段值，如果索引为-1或不存在则返回null
     */
    private String getFieldValue(ProductImportNewDto productImportDto, Map<String, Integer> fieldIndexMap, String fieldName) {
        Integer index = fieldIndexMap.get(fieldName);
        if (index == null || index == -1) {
            return null;
        }

        // 根据索引获取对应的text属性值
        switch (index) {
            case 0: return productImportDto.getText0();
            case 1: return productImportDto.getText1();
            case 2: return productImportDto.getText2();
            case 3: return productImportDto.getText3();
            case 4: return productImportDto.getText4();
            case 5: return productImportDto.getText5();
            case 6: return productImportDto.getText6();
            case 7: return productImportDto.getText7();
            case 8: return productImportDto.getText8();
            case 9: return productImportDto.getText9();
            case 10: return productImportDto.getText10();
            case 11: return productImportDto.getText11();
            case 12: return productImportDto.getText12();
            case 13: return productImportDto.getText13();
            case 14: return productImportDto.getText14();
            case 15: return productImportDto.getText15();
            case 16: return productImportDto.getText16();
            case 17: return productImportDto.getText17();
            case 18: return productImportDto.getText18();
            case 19: return productImportDto.getText19();
            case 20: return productImportDto.getText20();
            case 21: return productImportDto.getText21();
            case 22: return productImportDto.getText22();
            case 23: return productImportDto.getText23();
            case 24: return productImportDto.getText24();
            case 25: return productImportDto.getText25();
            case 26: return productImportDto.getText26();
            case 27: return productImportDto.getText27();
            case 28: return productImportDto.getText28();
            case 29: return productImportDto.getText29();
            case 30: return productImportDto.getText30();
            default: return null;
        }
    }


    /**
     * 递归构建sku
     *
     * @param specList 规格参数
     */
    private List<ProductSkuBo> buildSku(List<SpecCombinationBo> specList) {
        return skuCombinationAssist.generateCombinations(specList, 0l);
    }

    /**
     * 构建单规格
     */
    private ProductSkuBo buildSingleSpec(ProductImportDto product) {
        ProductSkuBo sku = JsonUtil.copy(product, ProductSkuBo.class);
        sku.setSkuId(String.format(CommonConstant.SINGLE_SKU_ID_FORMAT, 0));
        sku.setSkuCode(product.getProductCode());
        return sku;
    }

    /**
     * 补齐sku数据
     */
    private void appendSkuList(ProductImportDto product, List<ProductSkuBo> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        for (int i = 0; i < skuList.size(); i++) {
            ProductSkuBo sku = skuList.get(i);
            sku.setMeasureUnit(product.getMeasureUnit());
            sku.setStock(StringUtils.isEmpty(product.getStock()) ? 0 : Long.parseLong(product.getStock()));
            sku.setSalePrice(BigDecimal.valueOf(Double.parseDouble(product.getSalePrice())));
            sku.setSkuCode(product.getProductCode() + CommonConstant.SKU_CODE_SPLIT + i);
        }
    }

    /**
     * 构建错误信息
     *
     * @param productList 导入的数据
     */
    private void buildErrMsg(List<ProductImportNewDto> productList) {
        productList.forEach(product -> {
            String errMsg = StringUtils.defaultString(product.getErrMsg(), StrUtil.EMPTY);
            product.setErrMsg(errMsg + product.getErrBuilder().toString());
        });
    }

    /**
     * 调用OE零件数据清洗接口
     *
     * @param oeNo OE号
     */
    private ThirdApiOeDto callOeDataCleaningApi(String oeNo) {
        ThirdApiOeDto dto = new ThirdApiOeDto();
        if (StrUtil.isBlank(oeNo)) {
            dto.setErrMsg("【零件接口】OE号为空，跳过调用OE零件数据清洗接口");
            return dto;
        }
        try {
            // 构建请求URL
            String url = chengpeiApiUrl + "/api/system/oe?oe_no=" + oeNo;
            log.info("开始调用OE零件数据清洗接口，OE号：{}, URL：{}", oeNo, url);
            // 发送GET请求
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("OE零件数据清洗接口调用成功，响应：{}", responseBody);
                // 解析响应结果
                dto = parseOeApiResponse(oeNo, responseBody, dto);
            } else {
                dto.setErrMsg("【零件接口】OE零件数据清洗接口调用失败，状态码：" + response.getStatusCode());
            }

        } catch (Exception e) {
            dto.setErrMsg("【零件接口】调用OE零件数据清洗接口异常，OE号：" + oeNo + "，msg:" + e.getMessage());
        }
        return dto;
    }

    /**
     * 解析OE接口响应结果
     *
     * @param oeNo OE号
     * @param responseBody 响应体
     */
    private ThirdApiOeDto parseOeApiResponse(String oeNo, String responseBody, ThirdApiOeDto dto) {

        try {
            JsonNode jsonNode = objectMapper.readTree(responseBody);

            int code = jsonNode.get("code").asInt();
            String message = jsonNode.get("message").asText();

            if (code == 1) {
                // 成功
                JsonNode dataNode = jsonNode.get("data");
                if (dataNode != null) {
                    String partsInfo = dataNode.get("parts_info") != null ? dataNode.get("parts_info").asText() : "";
                    String partsName = dataNode.get("parts_name") != null ? dataNode.get("parts_name").asText() : "";
                    String replaceNo = dataNode.get("replase_no") != null ? dataNode.get("replase_no").asText() : "";
                    String cars = dataNode.get("cars") != null ? dataNode.get("cars").asText() : "";
                    log.info("【零件接口】OE零件数据解析成功 - OE号：{}, 零件信息：{}, 零件名称：{}, 替换件：{}, 适用车型：{}",
                            oeNo, partsInfo, partsName, replaceNo, cars);
                    // TODO: 根据业务需要处理解析后的数据
                    dto.setCars(cars);
                    dto.setReplaceNo(replaceNo);
                    dto.setPartsInfo(partsInfo);
                    dto.setPartsName(partsName);
                } else {
                    dto.setErrMsg("【零件接口】返回OE零件数据为空，OE号：" + oeNo);
                }
            } else if (code == 0) {
                dto.setErrMsg("【零件接口】OE零件数据查询失败，OE号："+oeNo+"，原因：" + message);
            } else if (code == 2) {
                dto.setErrMsg("【零件接口】未找到对应OE零件数据，OE号：" + oeNo);
            } else {
                dto.setErrMsg("【零件接口】OE零件数据查询返回未知状态码，OE号："+oeNo+"，状态码："+code+"，消息：" + message);
            }
        } catch (Exception e) {
            dto.setErrMsg("【零件接口】解析OE接口响应异常，OE号："+oeNo+"，响应：" + responseBody + "，msg:" + e.getMessage());
        }
        return dto;
    }

}
