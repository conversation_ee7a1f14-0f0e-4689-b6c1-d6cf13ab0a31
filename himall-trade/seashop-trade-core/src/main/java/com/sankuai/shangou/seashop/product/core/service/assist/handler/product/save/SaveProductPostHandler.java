package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecValueMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerProcessor;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventBuilder;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.SaveProductSkuResp;

import lombok.extern.slf4j.Slf4j;

/**
 * 保存商品后置处理器
 *
 * <AUTHOR>
 * @date 2023/12/29 11:40
 */
@Component
@Slf4j
public class SaveProductPostHandler extends AbsProductHandler {

    @Resource
    private ProductHandlerProcessor productHandlerProcessor;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private SkuAssist skuAssist;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SpecValueMapper specValueMapper;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;


    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】后置处理器,提交审核【start】, context:{}", context);
        Product product = buildSaveResult(context);

        // 发送商品变动事件
        SendProductChangeEvent event = SendProductChangeEvent
                .build(product.getProductId(), product.getShopId(), context.getChangeSource(), context.getChangeType());
        transactionEventPublisher.publish(event);

        if (!context.isNeedAudit()) {
            log.info("【保存商品】后置处理器, 草稿或者不需要审核, 流程结束【end】, productId： {}", context.getProductId());
            return;
        }

        // 提交审批后自动提交风控
        productHandlerProcessor.handle(ProductHandlerType.SUBMIT_PRODUCT_AUDIT, context);

        log.info("【保存商品】后置处理器, 提交审核成功【end】, context:{}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT_POST;
    }


    /**
     * 构建保存结果
     *
     * @param context 保存商品上下文对象
     */
    private Product buildSaveResult(ProductContext context) {
        Long productId = context.getProductId();
        Product product = productRepository.getByProductId(productId);
        SaveProductResp productResp = JsonUtil.copy(product, SaveProductResp.class);

        List<Sku> skuList = skuRepository.list(new LambdaQueryWrapper<Sku>().eq(Sku::getProductId, productId));
        productResp.setSkuList(JsonUtil.copyList(skuList, SaveProductSkuResp.class, (source, target) -> {
            target.setSkuAutoId(String.valueOf(source.getId()));
            target.setSkuName(skuAssist.getSkuName(source.getSpec1Value(), source.getSpec2Value(), source.getSpec3Value()));
        }));
        context.setSaveProductResp(productResp);
        return product;
    }
}
