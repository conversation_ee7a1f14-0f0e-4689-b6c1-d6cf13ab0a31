package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.Date;

/**
 * <p>
 * 首页广告设置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-10
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class AdvanceSaveBo extends BaseParamReq {

    /**
     * 是否开启弹窗广告
     */
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    private String img;

    /**
     * 图片外联链接
     */
    private String link;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 是否重复播放
     */
    private Boolean isReplay;
}
