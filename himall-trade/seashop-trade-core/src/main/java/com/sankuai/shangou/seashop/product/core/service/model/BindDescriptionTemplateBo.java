package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/20 14:48
 */
@Getter
@Setter
@Builder
public class BindDescriptionTemplateBo extends BaseParamReq {

    /**
     * 商品id的集合
     */
    private List<Long> productIdList;

    /**
     * 顶部版式id 不关联传0
     */
    private Long descriptionPrefixId;

    /**
     * 底部版式id 不关联传0
     */
    private Long descriptionSuffixId;

    /**
     * 店铺id
     */
    private Long shopId;

}
