package com.sankuai.shangou.seashop.seller.thrift.core.service.promotion;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiShopFlashSaleConfigReq;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@InterfaceDoc(
    type = "thrift",
    displayName = "限时购配置操作服务",
    description = "限时购配置操作服务",
    scenarios = "主要应用于限时购配置操作服务的场景",
    host = ""
)
@ThriftService
public interface ApiFlashSaleConfigCmdThriftService {

    /**
     * 修改店铺限时购配置
     *
     * @param request
     * @return
     * @throws TException
     */
    @MethodDoc(
        displayName = "修改店铺限时购配置",
        description = "修改店铺限时购配置",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "request", description = "请求体", requiredness = Requiredness.REQUIRED)
        },
        returnValueDescription = "保存结果",
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<BaseResp> updateShopConfig(ApiShopFlashSaleConfigReq request, ShopUserInfo shopInfo) throws TException;
}
