package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class DiscountActiveRuleBo extends BaseThriftDto {

    @PrimaryField
    private Long id;

    private Long activeId;

    @ExaminField
    private BigDecimal quota;

    @ExaminField
    private BigDecimal discount;
}
