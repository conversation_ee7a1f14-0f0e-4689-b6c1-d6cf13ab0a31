package com.sankuai.shangou.seashop.promotion.core.model.bo;//package com.sankuai.shangou.seashop.promotion.core.model.bo;
//
//import com.meituan.servicecatalog.api.annotations.FieldDoc;
//import com.meituan.servicecatalog.api.annotations.TypeDoc;
//import lombok.*;
//
//import java.util.Date;
//
///**
// * @author: lhx
// * @date: 2023/11/3/003
// * @description:
// */
//@TypeDoc(description = "折扣活动响应体")
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//@ToString
//public class DiscountActiveListBo {
//
//    //主键ID
//    private Long id;
//
//    //店铺ID
//    private Long shopId;
//
//    //活动名称
//    private String activeName;
//
//    //开始时间
//    private Date startTime;
//
//    //结束时间
//    private Date endTime;
//
//    //状态
//    private Integer status;
//
//    //状态名称
//    private String statusDesc;
//
//    //商品数量
//    private Integer productCount;
//
//}
