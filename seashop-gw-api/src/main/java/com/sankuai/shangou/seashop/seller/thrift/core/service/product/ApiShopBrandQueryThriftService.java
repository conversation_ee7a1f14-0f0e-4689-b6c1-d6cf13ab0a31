package com.sankuai.shangou.seashop.seller.thrift.core.service.product;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiShopBrandListResp;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:25
 */
@InterfaceDoc(
    type = "thrift",
    displayName = "商家品牌查询服务",
    description = "商家品牌查询服务",
    scenarios = "商家品牌查询服务",
    host = ""
)
@ThriftService
public interface ApiShopBrandQueryThriftService {

    @MethodDoc(
        displayName = "查询商家品牌列表",
        description = "查询商家品牌列表",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"),
        },
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<ApiShopBrandListResp> queryShopBrandForList(ShopUserInfo shopInfo) throws TException;

}
