package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "店铺专享价适用商品对象")
public class ShopExclusivePriceProductDto extends BaseThriftDto {

    @Schema(description = "商品ID")
    private Long productId;
    @Schema(description = "skuId")
    private String skuId;
    @Schema(description = "专享价")
    private BigDecimal price;


}
