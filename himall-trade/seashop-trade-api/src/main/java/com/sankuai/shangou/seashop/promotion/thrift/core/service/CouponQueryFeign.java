package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponProductDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ProductAndShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductAvailableQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.ProductAvailableQueryResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 提供优惠券查询功能
 *
 * author: lhx
 * date: 2023/11/8/008
 */
@FeignClient(name = "himall-trade",contextId = "CouponQueryFeign", path = "/himall-trade/coupon", url = "${himall-trade.dev.url:}")
public interface CouponQueryFeign {

    /**
     * 优惠券活动列表查询
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<CouponSimpleResp>> pageList(@RequestBody CouponQueryReq request) throws TException;

    /**
     * 查询店铺是否有可领的优惠券
     */
    @GetMapping(value = "/flagHasCoupons")
    ResultDto<Boolean> flagHasCoupons(@RequestParam Long shopId) throws TException;

    /**
     * 通过id查询优惠券信息
     */
    @PostMapping(value = "/getById", consumes = "application/json")
    ResultDto<CouponResp> getById(@RequestBody BaseIdReq request) throws TException;

    /**
     * 查询优惠券关联商品信息
     */
    @PostMapping(value = "/queryCouponProductPage", consumes = "application/json")
    ResultDto<BasePageResp<CouponProductDto>> queryCouponProductPage(@RequestBody CouponProductQueryReq request) throws TException;

    /**
     * 通过用户ID查询可用的优惠券数量
     */
    @GetMapping(value = "/queryAvailableCouponCountByUser")
    ResultDto<Integer> queryAvailableCouponCountByUser(@RequestParam Long userId) throws TException;

    /**
     * 通过商品ID查询当前商品可用的优惠券信息
     */
    @PostMapping(value = "/queryByProductId", consumes = "application/json")
    ResultDto<CouponSimpleListResp> queryByProductId(@RequestBody ProductAndShopIdReq request) throws TException;

    /**
     * 通过id列表查询优惠券信息
     */
    @PostMapping(value = "/getByIdList", consumes = "application/json")
    ResultDto<CouponSimpleListResp> getByIdList(@RequestBody BaseBatchIdReq request) throws TException;

    /**
     * 通过消息id列表查询优惠券信息
     */
    @PostMapping(value = "/getByMessageId", consumes = "application/json")
    ResultDto<CouponSimpleListResp> getByMessageId(@RequestBody BaseIdReq request) throws TException;

    /**
     * 计算使用率
     */
    @PostMapping(value = "/calculateUsageRate", consumes = "application/json")
    ResultDto<String> calculateUsageRate(@RequestBody BaseIdReq msgId) throws TException;

    /**
     * 查询商品可用优惠券列表
     */
    @PostMapping(value = "/getProductAvailableCouponList", consumes = "application/json")
    ResultDto<ProductAvailableQueryResp> getProductAvailableCouponList(@RequestBody ProductAvailableQueryReq request) throws TException;

    /**
     * 查询优惠券关联商品ID
     */
    @GetMapping(value = "/queryRelateProductIds")
    ResultDto<List<Long>> queryRelateProductIds(@RequestParam Long couponId) throws TException;
}
