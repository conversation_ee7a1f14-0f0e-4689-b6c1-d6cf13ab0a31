package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "限时购查询请求对象")
public class FlashSaleQueryReq extends BasePageReq {

    @Schema(description = "活动ID")
    private Long id;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "活动名称")
    private String title;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始")
    private Integer status;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Override
    public void checkParameter() {
        if (null != this.startTime && null != this.endTime) {
            if (this.startTime.after(this.endTime)) {
                throw new IllegalArgumentException("开始时间不能大于结束时间");
            }
        }
    }


}
