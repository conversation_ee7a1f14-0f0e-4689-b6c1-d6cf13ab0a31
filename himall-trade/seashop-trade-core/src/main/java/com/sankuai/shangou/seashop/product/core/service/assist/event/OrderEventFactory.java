package com.sankuai.shangou.seashop.product.core.service.assist.event;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.OrderMessage;
import com.sankuai.shangou.seashop.product.core.service.assist.event.handler.AbstractOrderHandler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/10/31 10:17
 */
@Component
@Slf4j
public class OrderEventFactory {


    public void handle(OrderMessage message) {
        OrderMessageEventEnum event = getEvent(message.getOrderEventName());
        if (event == null) {
            log.warn("[订单变动] 收到无效的事件, body: {}", JsonUtil.toJsonString(message));
        }

        Map<String, AbstractOrderHandler> handlerMap = SpringUtil.getBeansOfType(AbstractOrderHandler.class);
        List<AbstractOrderHandler> handlerList = handlerMap.values().stream().filter(h -> h.getEvent().equals(event)).collect(Collectors.toList());
        if (CollUtil.isEmpty(handlerList)) {
            log.warn("[订单变动] 收到事件, 但没有对应的处理器, body: {}", JsonUtil.toJsonString(message));
            return;
        }

        // 并发处理 todo 后续在这里做幂等
        String lockName = LockConstant.LOCK_ORDER_CHANGE_EVENT_PATTERN.replace("{0}", message.getOrderId());
        LockHelper.lock(lockName, () -> {
            handlerList.forEach(h -> {
                log.info("[订单变动] 处理事件, handler: {}, body: {}", h.getEvent(), JsonUtil.toJsonString(message));
                h.handle(message);
            });
        });
        log.info("[订单变动] 处理完成, body: {}", JsonUtil.toJsonString(message));
    }

    private OrderMessageEventEnum getEvent(String name) {
        try {
            return OrderMessageEventEnum.valueOf(name);
        } catch (Exception e) {
            return null;
        }
    }

}
