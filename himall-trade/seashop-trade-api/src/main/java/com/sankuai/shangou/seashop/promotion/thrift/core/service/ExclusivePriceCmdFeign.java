package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceProductUpdateReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ExclusivePriceSaveReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description: 提供专享价活动操作功能
 */
@FeignClient(name = "himall-trade",contextId = "ExclusivePriceCmdFeign", path = "/himall-trade/exclusivePriceProduct", url = "${himall-trade.dev.url:}")
public interface ExclusivePriceCmdFeign {

    /**
     * 保存专享价活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody ExclusivePriceSaveReq request) throws TException;

    /**
     * 结束专享价活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/endActive", consumes = "application/json")
    ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException;

    /**
     * 修改专享价活动商品信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateProduct", consumes = "application/json")
    ResultDto<BaseResp> updateProduct(@RequestBody ExclusivePriceProductUpdateReq request) throws TException;
}
