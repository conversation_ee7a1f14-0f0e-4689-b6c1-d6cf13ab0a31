package com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto.DescriptionTemplateDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:35
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "查询版式列表返回值")
public class DescriptionTemplateListResp extends BaseThriftDto {

    @Schema(description = "版式列表")
    private List<DescriptionTemplateDto> templateList;


}
