package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class FullReductionSaveBo extends BaseParamReq {

    @PrimaryField
    private Long id;

    /**
     * 店铺编号
     */
    @ExaminField
    private Long shopId;

    /**
     * 店铺名称
     */
    @ExaminField
    private String shopName;

    /**
     * 活动名称
     */
    @ExaminField
    private String activeName;

    /**
     * 单笔订单满减金额门槛
     */
    @ExaminField
    private BigDecimal moneyOffCondition;

    /**
     * 单笔订单满减金额
     */
    @ExaminField
    private BigDecimal moneyOffFee;

    /**
     * 是否叠加优惠
     */
    @ExaminField
    private Boolean moneyOffOverLay;

    /**
     * 开始时间
     */
    @ExaminField
    private Date startTime;

    /**
     * 结束时间
     */
    @ExaminField
    private Date endTime;
}
