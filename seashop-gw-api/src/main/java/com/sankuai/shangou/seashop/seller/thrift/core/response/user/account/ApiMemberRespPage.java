package com.sankuai.shangou.seashop.seller.thrift.core.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签信息
 * @author: LXH
 **/
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "标签信息")
public class ApiMemberRespPage {
    @FieldDoc(description = "标签信息列表")
    private BasePageResp<ApiMemberResp> pageResp;


}
