package com.sankuai.shangou.seashop.product.core.service.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/10 16:05
 */
@Getter
@Setter
public class SaveCategoryBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 类目的路径|隔开
     */
    private String path;

    /**
     * 上级的路径
     */
    private String parentPath;

    /**
     * 是否显示
     */
    private Boolean whetherShow;

    /**
     * 排序
     */
    private Long displaySequence;

}
