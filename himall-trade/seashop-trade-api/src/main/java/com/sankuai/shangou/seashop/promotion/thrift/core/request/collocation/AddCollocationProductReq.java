package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:09
 */
@Data
@Schema(description = "新增组合购商品入参")
public class AddCollocationProductReq extends BaseParamReq {

    @Schema(description = "商品ID")
    @ExaminField(description = "商品ID")
    private String productId;

    @Schema(description = "组合购ID")
    @ExaminField(description = "组合购ID")
    private Long colloId;

    @Schema(description = "是否主商品")
    @ExaminField(description = "是否主商品")
    private Boolean mainFlag;

    @Schema(description = "排序,0开始计算")
    @ExaminField(description = "排序,0开始计算")
    private Integer displaySequence;

    @Schema(description = "组合购商品SKU集合")
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationSku", description = "组合购商品SKU集合")
    private List<AddCollocationSkuReq> skuReqList;


}
