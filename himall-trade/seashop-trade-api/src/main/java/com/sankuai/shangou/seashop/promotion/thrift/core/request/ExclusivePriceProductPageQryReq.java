package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "专享价活动商品查询请求对象")
public class ExclusivePriceProductPageQryReq extends BasePageReq {

    @Schema(description = "活动ID", required = true)
    private Long activeId;


}
