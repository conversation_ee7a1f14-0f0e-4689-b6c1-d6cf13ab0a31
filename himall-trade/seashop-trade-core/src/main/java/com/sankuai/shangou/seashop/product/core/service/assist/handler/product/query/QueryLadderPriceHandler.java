package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.LadderPriceAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 阶梯价查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:22
 */
@Component
@Slf4j
public class QueryLadderPriceHandler extends AbsProductHandler {

    @Resource
    private LadderPriceAssist ladderPriceAssist;


    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        Long productId = context.getProductId();

        // 查询阶梯价
        productBo.setLadderPriceList(ladderPriceAssist.getLadderPriceBoList(productId));
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_LADDER_PRICE;
    }


}
