package com.sankuai.shangou.seashop.m.thrift.order.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.m.thrift.order.dto.ApiOrderExpressDto;
import com.sankuai.shangou.seashop.m.thrift.order.dto.ApiOrderInvoiceDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单明细返回对象")
@Getter
@Setter
public class ApiOrderDetailResp {

    @FieldDoc(description = "状态变更的时间，前端根据数量来判断和显示步骤")

    private List<String> statusChangeTimeList;
    @FieldDoc(description = "订单基本信息")

    private ApiOrderInfoDto orderInfo;
    @FieldDoc(description = "订单发票信息")

    private ApiOrderInvoiceDto orderInvoice;
    @FieldDoc(description = "订单物流信息")

    private List<ApiOrderExpressDto> expressList;

}
