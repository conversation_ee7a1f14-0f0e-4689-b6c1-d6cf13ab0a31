package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:55
 */
@Getter
@Setter
public class UpdateVirtualSalesItemLogBo {

    @ExaminField(description = "商品ID")
    @PrimaryField
    private Long productId;

    @ExaminField(description = "虚拟销量")
    private Long virtualSaleCounts;

}
