package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.math.BigDecimal;
import java.util.List;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductField;
import com.sankuai.shangou.seashop.product.core.service.assist.comparator.NullAsEmptyStringComparator;
import com.sankuai.shangou.seashop.product.thrift.core.dto.SpecDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSkuBo {

    /**
     * sku 自增Id
     */
    private Long skuAutoId;

    /**
     * skuId 商品id_规格1Id_规格2Id_规格3Id
     */
    private String skuId;

    /**
     * 规格1 Id
     */
    @ProductField(needAudit = true)
    private Long spec1ValueId;

    /**
     * 规格2 Id
     */
    @ProductField(needAudit = true)
    private Long spec2ValueId;

    /**
     * 规格3 Id
     */
    @ProductField(needAudit = true)
    private Long spec3ValueId;

    /**
     * 规格1 值
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "规格1值")
    private String spec1Value;

    /**
     * 规格2 值
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "规格2值")
    private String spec2Value;

    /**
     * 规格3 值
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "规格3值")
    private String spec3Value;

    /**
     * 销售价
     */
    @ProductField(needAudit = true, type = BigDecimal.class)
    @ExaminField(description = "销售价")
    private BigDecimal salePrice;

    /**
     * 库存
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "库存")
    private Long stock;

    /**
     * 货号
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "货号")
    private String skuCode;

    /**
     * 警戒库存
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "警戒库存")
    private Long safeStock;

    /**
     * 显示图片
     */
    @ProductField(needAudit = true, comparator = NullAsEmptyStringComparator.class)
    @ExaminField(description = "显示图片")
    private String showPic;

    /**
     * 计量单位
     */
    @ProductField(needAudit = false)
    @ExaminField(description = "计量单位")
    private String measureUnit;

    /**
     * 库存表自增id
     */
    private Long skuStockId;

    /**
     * 规格值集合
     */
    private List<SpecDto> specList;

    /**
     * 规格值json
     */
    @ProductField(needAudit = false, comparator = NullAsEmptyStringComparator.class)
    private String specValueJson;
}
