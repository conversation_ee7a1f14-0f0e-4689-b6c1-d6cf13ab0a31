package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.VirtualSaleCountsTypeEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/21 17:22
 */
@Setter
@Getter
public class ProductUpdateVirtualSaleCountsBo extends BaseParamReq {

    /**
     * 商品id的集合
     */
    private List<Long> productIdList;

    /**
     * 虚拟销量类型
     */
    private VirtualSaleCountsTypeEnum virtualSaleCountsType;

    /**
     * 固定数
     */
    private Long fixedNum;

    /**
     * 最小随机数
     */
    private Long minRandomNum;

    /**
     * 最大随机数
     */
    private Long maxRandomNum;
}
