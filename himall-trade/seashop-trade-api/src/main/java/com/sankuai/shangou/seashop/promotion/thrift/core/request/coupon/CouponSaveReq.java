package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.constant.PromotionApiConstant;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.ReceiveTypeEnum;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.UseAreaEnum;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/7/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存优惠券请求对象")
public class CouponSaveReq extends BaseParamReq {

    @Schema(description = "主键ID")
    @PrimaryField(title = "主键")
    private Long id;

    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "面值(价格)", required = true)
    @ExaminField(description = "面值(价格)")
    private BigDecimal price;

    @Schema(description = "最大可领取张数", required = true)
    @ExaminField(description = "最大可领取张数")
    private Integer perMax;

    @Schema(description = "订单金额（满足多少钱才能使用）", required = true)
    @ExaminField(description = "订单金额（满足多少钱才能使用）")
    private BigDecimal orderAmount;

    @Schema(description = "发行张数", required = true)
    @ExaminField(description = "发行张数")
    private Integer num;

    @Schema(description = "开始时间", required = true)
    @ExaminField(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间", required = true)
    @ExaminField(description = "结束时间")
    private Date endTime;

    @Schema(description = "优惠券名称", required = true)
    @ExaminField(description = "优惠券名称")
    private String couponName;

    @Schema(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放", required = true)
    @ExaminField(description = "领取方式")
    private Integer receiveType;

    @Schema(description = "使用范围：0=全场通用，1=部分商品可用", required = true)
    @ExaminField(description = "使用范围")
    private Integer useArea;

    @Schema(description = "备注")
    @ExaminField(description = "备注")
    private String remark;

    @Schema(description = "产品ID列表")
    private List<Long> productIdList;

    @Schema(description = "推广方式：0 平台；4 移动端(小程序)")
    private List<Integer> platForm;

    @Override
    public void checkParameter() {
        if (this.shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (this.price == null) {
            throw new InvalidParamException("price不能为空");
        }
        if (this.price.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("price必须大于0");
        }
        if (this.perMax == null) {
            throw new InvalidParamException("perMax不能为空");
        }
        if (this.perMax > PromotionApiConstant.COUPON_MAX_RECEIVE_NUM) {
            throw new InvalidParamException("perMax不能大于" + PromotionApiConstant.COUPON_MAX_RECEIVE_NUM);
        }
        if (this.orderAmount == null) {
            throw new InvalidParamException("orderAmount不能为空");
        }
        if (this.orderAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("orderAmount必须大于0");
        }
        if (this.orderAmount.compareTo(BigDecimal.ZERO) > 0 && this.orderAmount.compareTo(this.price) < 0) {
            throw new InvalidParamException("orderAmount必须大于price");
        }
        if (this.num == null) {
            throw new InvalidParamException("num不能为空");
        }
        if (this.startTime == null) {
            throw new InvalidParamException("活动开始时间不能为空");
        }
        if (this.endTime == null) {
            throw new InvalidParamException("活动结束时间不能为空");
        }
        if (this.startTime.after(this.endTime)) {
            throw new InvalidParamException("活动开始时间不能大于活动结束时间");
        }
        if (this.endTime.before(new Date())) {
            throw new InvalidParamException("活动结束时间不能小于当前时间");
        }
        if (StringUtils.isBlank(this.couponName)) {
            throw new InvalidParamException("优惠券名称不能为空");
        }
        if (this.receiveType == null) {
            throw new InvalidParamException("receiveType不能为空");
        }
        if (this.useArea == null) {
            throw new InvalidParamException("useArea不能为空");
        }
        if (this.useArea.equals(UseAreaEnum.PRODUCT.getCode())) {
            if (CollectionUtils.isEmpty(this.productIdList)) {
                throw new InvalidParamException("productIdList不能为空");
            }
        }
        if (this.receiveType.equals(ReceiveTypeEnum.SHOP_INDEX.getCode())) {
            if (CollectionUtils.isEmpty(this.platForm)) {
                throw new InvalidParamException("platForm不能为空");
            }
        }
    }


}