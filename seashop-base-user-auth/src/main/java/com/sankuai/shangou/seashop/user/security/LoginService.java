package com.sankuai.shangou.seashop.user.security;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.sankuai.shangou.seashop.base.boot.dto.*;
import com.sankuai.shangou.seashop.base.boot.enums.LoginErrorEnum;
import com.sankuai.shangou.seashop.base.boot.enums.LoginTypeEnum;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.exception.LoginException;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.MessageCMDThriftService;
import com.sankuai.shangou.seashop.base.security.cache.LoginCacheKey;
import com.sankuai.shangou.seashop.base.security.cache.storage.TokenRedisStorage;
import com.sankuai.shangou.seashop.base.security.config.LoginSecurityConfig;
import com.sankuai.shangou.seashop.base.security.context.LoginStrategy;
import com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext;
import com.sankuai.shangou.seashop.base.security.event.LoginEvent;
import com.sankuai.shangou.seashop.base.security.event.LogoutEvent;
import com.sankuai.shangou.seashop.base.security.event.UserDeleteEvent;
import com.sankuai.shangou.seashop.base.security.event.UserDisableEvent;
import com.sankuai.shangou.seashop.base.security.handler.BaseLoginService;
import com.sankuai.shangou.seashop.base.security.utils.TokenUtil;
import com.sankuai.shangou.seashop.user.account.service.PrivilegeService;
import com.sankuai.shangou.seashop.user.common.config.UserLionConfigClient;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.domain.Privilege;
import com.sankuai.shangou.seashop.user.dao.account.domain.Role;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.MemberRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.RoleRepository;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.thrift.auth.request.LoginSmsReq;
import com.sankuai.shangou.seashop.user.thrift.auth.request.RefreshTokenReq;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: cdd
 * @date: 2024/5/14/014
 * @description: 账号说明：
 * manager表 存 商家账号（供应商）、平台管理账号
 * 平台管理账号:shop_id = 0 且 role_id=0，否则为供应商商家
 * <p>
 * member存放 会员账号，会员可以申请入驻成为供应商，会将member中的账号信息 同步到 manager表；
 * <p>
 * 登录:平台，供应商后台登录走 manager表
 * 会员: 走member登录
 */
@Service
@Slf4j
public class LoginService implements BaseLoginService {
    @Resource
    private ManagerRepository managerRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private ShopRepository shopRepository;

    @Resource
    private LoginStrategyContext loginStrategyContext;
    @Resource
    private MessageCMDThriftService messageCMDThriftService;
    @Resource
    private UserLionConfigClient userLionConfigClient;

    @Resource
    private PrivilegeService privilegeService;

    @Resource
    private TokenRedisStorage storage;


    /**
     * 基础登录信息
     *
     * @param loginBaseDto
     * @return
     */
    @Override
    public LoginManagerDto getManagerUser(LoginBaseDto loginBaseDto) {
        long userId = loginBaseDto.getId();
        LoginManagerDto managerDto = new LoginManagerDto();
        BeanUtil.copyProperties(loginBaseDto, managerDto);
        Manager manager = managerRepository.getById(userId);
        Role role = new Role();
        role.setShopId(manager.getShopId());
        List<Role> list = roleRepository.selectList(role);
        if (CollUtil.isNotEmpty(list)) {
            managerDto.setRoles(list.stream().map(Role::getId).collect(Collectors.toList()));
        }
        List<Privilege> privileges = privilegeService.queryUserPrivilegesByRoleIds(managerDto.getRoles());
        if (CollUtil.isNotEmpty(privileges)) {
            managerDto.setPrivileges(privileges.stream().map(Privilege::getUrl).collect(Collectors.toList()));
        }
        return managerDto;
    }

    @Override
    public LoginMemberDto getMemberUser(LoginBaseDto loginBaseDto) {
        LoginMemberDto memberDto = new LoginMemberDto();
        BeanUtil.copyProperties(loginBaseDto, memberDto);
        Member member = null;
        if (loginBaseDto.getId() != null) {
            member = memberRepository.getById(loginBaseDto.getId());
        } else {
            member = memberRepository.queryMemberByUserName(loginBaseDto.getName());
        }
        memberDto.setWeiXinOpenId(member.getOpenId());
        memberDto.setWeiXinUnionId(member.getUnionId());
        memberDto.setWxmpOpenId(member.getWxmpOpenId());
        memberDto.setWxmpUnionId(member.getWxmpUnionId());
        memberDto.setWeiXinUser(StrUtil.isNotBlank(member.getOpenId()) && StrUtil.isNotBlank(member.getUnionId()));
        return memberDto;
    }

    @Override
    public LoginShopDto getShopUser(LoginBaseDto loginBaseDto) {
        LoginShopDto shopDto = new LoginShopDto();
        BeanUtil.copyProperties(loginBaseDto, shopDto);
        Manager manager = managerRepository.getById(loginBaseDto.getId());
        shopDto.setManagerId(manager.getId());
        shopDto.setManagerName(manager.getUserName());
        shopDto.setShopId(manager.getShopId());
        Shop shop = shopRepository.getById(manager.getShopId());
        shopDto.setName(shop.getShopName());
        Member member = memberRepository.queryMemberByUserName(loginBaseDto.getName());
        if (Objects.nonNull(member)) {
            shopDto.setUserId(member.getId());
            shopDto.setUserName(member.getUserName());
        }
        Role role = new Role();
        role.setShopId(manager.getShopId());
        List<Role> list = roleRepository.selectList(role);
        if (CollUtil.isNotEmpty(list)) {
            shopDto.setRoles(list.stream().map(Role::getId).collect(Collectors.toList()));
        }
        List<Privilege> privileges = privilegeService.queryUserPrivilegesByRoleIds(shopDto.getRoles());
        if (CollUtil.isNotEmpty(privileges)) {
            shopDto.setPrivileges(privileges.stream().map(Privilege::getUrl).collect(Collectors.toList()));
        }
        shopDto.setId(manager.getShopId());
        return shopDto;
    }

    @Override
    public boolean disableMember(Long userId) {
        Member member = memberRepository.getById(userId);
        if (member == null) {
            log.warn("用户不存在，id:{}", userId);
            return false;
        }
        member.setDisabled(true);
        memberRepository.updateById(member);

        return disable(RoleEnum.MEMBER, userId);
    }

    /**
     * 登录
     *
     * @param req
     * @return
     */
    public LoginResp login(LoginReq req) {
        LoginTypeEnum loginType = LoginTypeEnum.valueOf(req.getLoginType());
        LoginStrategy loginStrategy = loginStrategyContext.getLoginStrategy(loginType);
        AssertUtil.throwIfNull(loginStrategy, "非法登录类型");
        //根据类型执行登录
        LoginBaseDto loginBaseDto = loginStrategy.process(req);
        AssertUtil.throwIfNull(loginBaseDto, LoginErrorEnum.FAILED.getMsg());
        //通用的处理登录及获取登录token
        TokenCache tokenCache = login(loginBaseDto);
        return LoginResp.builder()
                .loginType(LoginTypeEnum.valueOf(req.getLoginType()))
                .token(tokenCache)
                .haveNotPassword(loginBaseDto.getHaveNotPassword())
                .build();
    }

    /**
     * 获取登录安全配置
     *
     * @param roleEnum 登录类型
     * @return LoginSecurityConfig
     */
    public LoginSecurityConfig getLoginSecurityConfig(RoleEnum roleEnum) {
        return new LoginSecurityConfig();
    }

    /**
     * 登录
     *
     * @param loginBaseDto 登录基础信息
     * @return TokenCache
     * @throws LoginException 登录异常
     */
    public TokenCache login(LoginBaseDto loginBaseDto) throws LoginException {
        if (!Objects.isNull(loginBaseDto) && !Objects.isNull(loginBaseDto.getRoleType()) && !Objects.isNull(loginBaseDto.getId())) {
            RoleEnum userType = loginBaseDto.getRoleType();
            Long userId = loginBaseDto.getId();
            // 如果该用户为普通用户和商家时 设置当前用户是否有密码
            if (Arrays.asList(RoleEnum.SHOP, RoleEnum.MEMBER).contains(userType)) {
                Member member = this.memberRepository.getById(userId);
                loginBaseDto.setHaveNotPassword(StringUtils.isBlank(member.getPassword()));
            }
            String token = TokenUtil.createToken();
            long timeout = getLoginSecurityConfig(loginBaseDto.getRoleType()).getTimeout();
            TokenCache tokenCache = TokenCache.builder().token(token).expiresTime(System.currentTimeMillis() + timeout * 1000L).userId(userId).userType(userType.name()).kick("0").build();
            SpringUtil.publishEvent(new LoginEvent(this, tokenCache, loginBaseDto));
            return tokenCache;
        } else {
            log.error("登录处理用户数据时，缺少必须参数!");
            throw new LoginException(LoginErrorEnum.FAILED);
        }
    }

    /**
     * 根据登录的平台类型退出登录
     *
     * @return
     */
    public boolean logout(TokenCache tokenCache) {
        AssertUtil.throwIfNull(tokenCache, "未发现有效token");
        RoleEnum userType = RoleEnum.nameOf(tokenCache.getUserType());
        SpringUtil.publishEvent(new LogoutEvent(this, tokenCache.getToken(), userType));
        return true;
    }

    /**
     * 用户禁用
     *
     * @param userType 用户类型
     * @param userId   用户id
     * @return boolean
     */
    @Override
    public boolean disable(RoleEnum userType, Long userId) {
        String userIdKey = LoginCacheKey.getUserKey(userType, userId);
        String userJson = storage.get(userIdKey);
        if (StrUtil.isEmpty(userJson)) {
            log.info("[用户禁用] 用户登录已失效, userType: {}, userId : {}", userType, userId);
            return true;
        }

        SpringUtil.publishEvent(new UserDisableEvent(this, JsonUtil.parseObject(userJson, LoginBaseDto.class)));
        return true;
    }

    @Override
    public boolean delete(RoleEnum userType, Long userId) {
        String userIdKey = LoginCacheKey.getUserKey(userType, userId);
        String userJson = storage.get(userIdKey);
        if (StrUtil.isEmpty(userJson)) {
            log.info("[用户注销] 用户登录已失效, userType: {}, userId : {}", userType, userId);
            return true;
        }

        SpringUtil.publishEvent(new UserDeleteEvent(this, JsonUtil.parseObject(userJson, LoginBaseDto.class)));
        return true;
    }

    /**
     * 发送登录短信
     *
     * @param req
     * @return
     */
    public BaseResp sendSms(LoginSmsReq req) {
        messageCMDThriftService.sendSmsCode(userLionConfigClient.getVerificationCode(), req.getPhone());
        return BaseResp.of();
    }

    public LoginResp refreshToken(RefreshTokenReq req) {
        TokenCache oldTokenCache = req.getToken();
        String token = TokenUtil.createToken();
        RoleEnum userType = RoleEnum.nameOf(oldTokenCache.getUserType());
        RoleEnum newRole = RoleEnum.nameOf(req.getRoleType());
        long timeout = getLoginSecurityConfig(newRole).getTimeout();
        if (!RoleEnum.SHOP.equals(userType) && !RoleEnum.MEMBER.equals(userType)) {
            throw new LoginException(LoginErrorEnum.FAILED);
        }
        //从redis缓存中获取
        Object userJson = storage.getObject(LoginCacheKey.getUserKey(userType, oldTokenCache.getUserId()));
        LoginMemberDto loginMemberDto = JsonUtil.parseObject(JsonUtil.toJsonString(userJson), LoginMemberDto.class);
        if (Objects.isNull(loginMemberDto) || loginMemberDto.getManagerId() == null) {
            throw new LoginException(LoginErrorEnum.FAILED);
        }

        Shop shop = shopRepository.getById(loginMemberDto.getShopId());
        if (shop == null) {
            throw new LoginException(LoginErrorEnum.NOT_SELLER);
        }
        if (!ShopEnum.AuditStatus.Open.getCode().equals(shop.getShopStatus())) {
            throw new LoginException(LoginErrorEnum.SHOP_NOT_ENABLE);
        }

        LoginBaseDto loginBaseDto = new LoginBaseDto();
        loginBaseDto.setId(loginMemberDto.getManagerId());
        loginBaseDto.setRoleType(newRole);
        LoginShopDto loginShopDto = getShopUser(loginBaseDto);
        TokenCache tokenCache = TokenCache.builder()
                .token(token)
                .expiresTime(System.currentTimeMillis() + timeout * 1000L)
                .userId(loginShopDto.getManagerId())
                .userType(newRole.name()).kick("0").build();
        // SpringUtil.publishEvent(new LogoutEvent(this, oldTokenCache.getToken(), userType));
        SpringUtil.publishEvent(new LoginEvent(this, tokenCache, loginShopDto));
        return LoginResp.builder()
                .loginType(LoginTypeEnum.TOKEN)
                .token(tokenCache)
                .build();
    }
}
