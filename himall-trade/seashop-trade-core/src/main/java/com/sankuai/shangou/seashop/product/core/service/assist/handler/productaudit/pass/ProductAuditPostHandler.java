package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAuditAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventBuilder;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.TransactionEventPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import lombok.extern.slf4j.Slf4j;

/**
 * 移除历史审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
@Order(0)
public class ProductAuditPostHandler extends AbsProductAuditPassHandler {

    @Resource
    private ProductAuditAssist productAuditAssist;
    @Resource
    private TransactionEventPublisher transactionEventPublisher;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        log.info("【商品审核通过】移除旧的审核记录【start】, productId: {}", productId);

        productAuditAssist.removeHistoryAudit(productId);

        // 发送商品变动通知
        SendProductChangeEvent event = SendProductChangeEvent
                .build(productId, context.getShopId(), ProductSourceEnum.MALL, ProductChangeType.AUDIT_PASS);
        transactionEventPublisher.publish(event);

        log.info("【商品审核通过】移除旧的审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.PASS_PRODUCT_AUDIT_POST;
    }

}
