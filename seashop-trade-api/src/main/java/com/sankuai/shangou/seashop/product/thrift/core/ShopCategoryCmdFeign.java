package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.DeleteShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.SaveShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.TransferProductReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 店铺分类操作服务
 *
 * author HuBiao
 * date 2023/11/13 11:05
 */
@FeignClient(name = "himall-trade", contextId = "ShopCategoryCmdFeign", path = "/himall-trade/shopCategory", url = "${himall-trade.dev.url:}")
public interface ShopCategoryCmdFeign {

    /**
     * 新增店铺分类
     */
    @PostMapping(value = "/createShopCategory", consumes = "application/json")
    ResultDto<BaseResp> createShopCategory(@RequestBody SaveShopCategoryReq request) throws TException;

    /**
     * 编辑店铺分类
     */
    @PostMapping(value = "/updateShopCategory", consumes = "application/json")
    ResultDto<BaseResp> updateShopCategory(@RequestBody SaveShopCategoryReq request) throws TException;

    /**
     * 删除店铺分类
     */
    @PostMapping(value = "/deleteShopCategory", consumes = "application/json")
    ResultDto<BaseResp> deleteShopCategory(@RequestBody DeleteShopCategoryReq request) throws TException;

    /**
     * 转移商品
     */
    @PostMapping(value = "/transferProduct", consumes = "application/json")
    ResultDto<BaseResp> transferProduct(@RequestBody TransferProductReq request) throws TException;

}
