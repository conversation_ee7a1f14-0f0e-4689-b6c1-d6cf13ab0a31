package com.sankuai.shangou.seashop.seller.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/04/30 14:53
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询折扣商品入参")
public class ApiQueryDiscountActiveProductReq extends BasePageReq {

    @FieldDoc(description = "活动id", requiredness = Requiredness.REQUIRED)
    private Long activeId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(activeId, "活动id不能为空");
    }


}
