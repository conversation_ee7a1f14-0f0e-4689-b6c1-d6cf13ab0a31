package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.PromotionProductService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.CollocationFlashSaleProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductIdListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.PromotionProductFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/3/7/007
 * @description:
 */
@RestController
@RequestMapping("/promotionProduct")
public class PromotionProductController implements PromotionProductFeign {

    @Resource
    private PromotionProductService promotionProductService;

    @PostMapping(value = "/collocationFlashSaleProductId", consumes = "application/json")
    @Override
    public ResultDto<ProductIdListResp> collocationFlashSaleProductId(@RequestBody ShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("collocationFlashSaleProductId", request,
                req -> {
                    req.checkParameter();
                    List<Long> longs = promotionProductService.collocationFlashSaleProductIdList(req.getShopId());
                    return new ProductIdListResp(longs);
                });
    }

    @PostMapping(value = "/collocationFlashSaleProductIdIncludeSub", consumes = "application/json")
    @Override
    public ResultDto<ProductIdListResp> collocationFlashSaleProductIdIncludeSub(@RequestBody CollocationFlashSaleProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("collocationFlashSaleProductId", request,
                req -> {
                    req.checkParameter();
                    List<Long> longs = promotionProductService.collocationFlashSaleProductIdIncludeSub(req);
                    return new ProductIdListResp(longs);
                });
    }
}
