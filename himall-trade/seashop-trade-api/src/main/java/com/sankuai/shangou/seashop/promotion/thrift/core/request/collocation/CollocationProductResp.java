package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Data
@ToString
@Schema(description = "组合购商品")
public class CollocationProductResp extends BaseParamReq {

    @Schema(description = "组合购商品ID")
    private Long id;

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "商品主图")
    private String imagePath;

    @Schema(description = "组合购ID")
    private Long colloId;

    @Schema(description = "是否主商品")
    private Boolean mainFlag;

    @Schema(description = "排序")
    private Integer displaySequence;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "组合购商品SKU集合")
    private List<CollocationSkuResp> skuRespList;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "最低售价(组合购多个sku的最低价格)")
    private BigDecimal minSalePrice;

    @Schema(description = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "是否是多规格")
    private Boolean hasSku;


}
