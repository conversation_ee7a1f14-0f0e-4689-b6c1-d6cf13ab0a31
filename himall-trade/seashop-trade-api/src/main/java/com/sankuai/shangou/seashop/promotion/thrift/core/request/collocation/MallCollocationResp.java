package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 10:41
 */
@Data
@Schema(description = "组合购查询列表返参")
@ToString
public class MallCollocationResp extends BaseParamReq {

    @Schema(description = "组合购查询列表返参集合")
    private List<CollocationResp> collocationRespList;


}
