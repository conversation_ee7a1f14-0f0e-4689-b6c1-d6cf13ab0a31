package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@Schema(description = "满减活动列表响应体")
@Data
public class FullReductionResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "活动名称")
    private String activeName;

    @Schema(description = "单笔订单满减金额门槛")
    private BigDecimal moneyOffCondition;

    @Schema(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusDesc;

    @Schema(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;


}
