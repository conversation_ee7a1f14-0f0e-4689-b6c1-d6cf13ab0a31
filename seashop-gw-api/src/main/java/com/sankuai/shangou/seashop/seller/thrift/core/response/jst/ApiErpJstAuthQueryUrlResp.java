package com.sankuai.shangou.seashop.seller.thrift.core.response.jst;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * 聚水潭生成授权链接返回对象
 *
 * <AUTHOR>
 */
@TypeDoc(
        description = "聚水潭生成授权链接返回对象"
)
@Data
public class ApiErpJstAuthQueryUrlResp extends BaseThriftDto {
    /**
     * 授权链接
     */
    @FieldDoc(
        name = "url",
        description = "授权链接",
        requiredness = Requiredness.REQUIRED
    )
    private String url;

    public static ApiErpJstAuthQueryUrlResp build(String url) {
        ApiErpJstAuthQueryUrlResp resp = new ApiErpJstAuthQueryUrlResp();
        resp.setUrl(url);
        return resp;
    }


}
