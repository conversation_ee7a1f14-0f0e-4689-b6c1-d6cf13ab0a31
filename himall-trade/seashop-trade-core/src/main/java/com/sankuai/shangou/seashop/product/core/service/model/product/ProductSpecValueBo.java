package com.sankuai.shangou.seashop.product.core.service.model.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/14 17:09
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductSpecValueBo {

    /**
     * 规格值id
     */
    private Long specValueId;

    /**
     * 规格值
     */
    private String value;

    /**
     * 是否选中
     */
    private boolean selected;

    /**
     * 规格 1-规格1 2-规格2 3-规格3
     */
    private Integer spec;

}
