package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品信息查询请求对象")
public class ProductQueryReq extends BaseParamReq {

    @Schema(description = "商品ID", required = true)
    private Long productId;

    @Schema(description = "商品总额", required = true)
    private BigDecimal productAmount;

    @Override
    public void checkParameter() {
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("productId不能为空");
        }
        if (this.productAmount == null || this.productAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("productAmount不能为空");
        }
    }


}
