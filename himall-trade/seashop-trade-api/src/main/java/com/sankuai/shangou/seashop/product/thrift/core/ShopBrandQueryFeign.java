package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryShopBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.ShopBrandListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.ShopBrandDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:25
 * @Description 商家品牌查询服务
 */
@FeignClient(name = "himall-trade", contextId = "ShopBrandQueryFeign", path = "/himall-trade/shopBrand", url = "${himall-trade.dev.url:}")
public interface ShopBrandQueryFeign {

    /**
     * 分页查询商家品牌列表
     */
    @PostMapping(value = "/queryShopBrandForPage", consumes = "application/json")
    ResultDto<BasePageResp<ShopBrandDto>> queryShopBrandForPage(@RequestBody QueryShopBrandReq request) throws TException;

    /**
     * 查询商家品牌列表

     */
    @PostMapping(value = "/queryShopBrandForList", consumes = "application/json")
    ResultDto<ShopBrandListResp> queryShopBrandForList(@RequestBody QueryShopBrandReq request) throws TException;

}
