package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.constant.PromotionApiConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存折扣活动请求对象")
public class DiscountActiveSaveReq extends BaseParamReq {

    @Schema(description = "主键ID")
    @PrimaryField(title = "主键")
    private Long id;

    @Schema(description = "店铺ID", required = true)
    @ExaminField(description = "店铺ID")
    private Long shopId;

    @Schema(description = "活动名称", required = true)
    @ExaminField(description = "活动名称")
    private String activeName;

    @Schema(description = "开始时间", required = true)
    @ExaminField(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间", required = true)
    @ExaminField(description = "结束时间")
    private Date endTime;

    @Schema(description = "优惠条件", required = true)
    private List<DiscountActiveRuleReq> ruleList;

    @Schema(description = "产品ID列表")
    private List<Long> productIdList;

    @Override
    public void checkParameter() {
        if (this.shopId == null) {
            throw new InvalidParamException("店铺ID不能为空");
        }
        if (StringUtils.isBlank(this.activeName)) {
            throw new InvalidParamException("活动名称不能为空");
        }
        this.activeName = this.activeName.trim();
        // activeName名字不能为特殊字符
        if (!Pattern.compile(PromotionApiConstant.NORMAL_CHARACTER_REGEX).matcher(this.activeName).matches()) {
            throw new InvalidParamException("活动名称不能包含特殊字符");
        }
        if (this.startTime == null) {
            throw new InvalidParamException("开始时间不能为空");
        }
        if (this.endTime == null) {
            throw new InvalidParamException("结束时间不能为空");
        }
        if (this.startTime.after(this.endTime)) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
        if (this.endTime.before(new Date())) {
            throw new InvalidParamException("结束时间不能小于当前时间");
        }
        if (this.ruleList == null || this.ruleList.isEmpty()) {
            throw new InvalidParamException("优惠条件不能为空");
        }
        if (this.ruleList.size() > PromotionApiConstant.DISCOUNT_ACTIVE_MAX_LEVEL) {
            throw new InvalidParamException("优惠条件不能超过" + PromotionApiConstant.DISCOUNT_ACTIVE_MAX_LEVEL + "条");
        }
        // 调用 ruleList 的校验
        ruleList.forEach(DiscountActiveRuleReq::checkParameter);
        // 校验是否有相同的规则
        if (ruleList.size() >= 2) {
            // 获取相同的规则在 ruleList 中的索引
            for (int i = 0; i < ruleList.size(); i++) {
                for (int j = i + 1; j < ruleList.size(); j++) {
                    if (ruleList.get(i).getQuota().compareTo(ruleList.get(j).getQuota()) == 0) {
                        throw new InvalidParamException(String.format("层级%s和层级%s优惠门槛重复，保存失败", i + 1, j + 1));
                    }
                }
            }
        }
    }


}
