package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "平台限时购配置请求对象")
public class PlatFlashSaleConfigReq extends BaseParamReq {

    @Schema(description = "是否需要审核（平台配置）", required = true)
    private Boolean needAuditFlag;

    @Override
    public void checkParameter() {
        if (null == this.needAuditFlag) {
            throw new InvalidParamException("是否需要审核不能为空");
        }
    }


}
