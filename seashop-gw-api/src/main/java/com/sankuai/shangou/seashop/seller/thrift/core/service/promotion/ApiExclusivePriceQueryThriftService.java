package com.sankuai.shangou.seashop.seller.thrift.core.service.promotion;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.promotion.ApiExclusivePriceQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiExclusivePriceListResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.promotion.ApiExclusivePriceResp;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@InterfaceDoc(
    type = "thrift",
    displayName = "专享价活动查询thrift服务",
    description = "提供专享价活动查询功能",
    scenarios = "主要应用于专享价活动查询的场景",
    host = "https://xframe.mws.cloud.test.sankuai.com"
)
@ThriftService
public interface ApiExclusivePriceQueryThriftService {

    /**
     * 优惠券活动列表查询
     *
     * @param request
     * @return
     * @throws TException
     */
    @MethodDoc(
        displayName = "专享价活动列表查询",
        description = "分页查询专享价活动列表",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "request", description = "请求体", requiredness = Requiredness.REQUIRED)
        },
        returnValueDescription = "查询结果",
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<BasePageResp<ApiExclusivePriceListResp>> pageList(ApiExclusivePriceQueryReq request, ShopUserInfo shopInfo) throws TException;

    /**
     * 通过id查询优惠券信息
     *
     * @param request
     * @return
     * @throws TException
     */
    @MethodDoc(
        displayName = "通过id查询专享价活动信息",
        description = "通过id查询专享价活动信息",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "request", description = "请求体", requiredness = Requiredness.REQUIRED)
        },
        returnValueDescription = "查询结果",
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<ApiExclusivePriceResp> getById(BaseIdReq request, ShopUserInfo shopInfo) throws TException;

}
