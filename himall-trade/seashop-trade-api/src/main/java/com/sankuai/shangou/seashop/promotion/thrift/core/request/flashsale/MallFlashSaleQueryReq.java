package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "限时购查询请求对象")
public class MallFlashSaleQueryReq extends BasePageReq {

    @Schema(description = "活动类型id")
    private Long categoryId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "会员id")
    private Long memberId;


}
