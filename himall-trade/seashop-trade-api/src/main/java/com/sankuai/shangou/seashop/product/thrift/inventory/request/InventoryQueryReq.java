package com.sankuai.shangou.seashop.product.thrift.inventory.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 15:08
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@Schema(description = "库存查询入参")
public class InventoryQueryReq extends BaseParamReq {

    @Schema(description = "商品id的集合")
    private List<Long> productIds;

    @Schema(description = "skuId的集合")
    private List<Long> skuIds;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIds) && CollectionUtils.isEmpty(skuIds), "商品id集合和skuId集合不能同时为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(productIds) && productIds.size() > 200, "商品id集合不能超过200个");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(skuIds) && skuIds.size() > 200, "skuId集合不能超过200个");
    }

}
