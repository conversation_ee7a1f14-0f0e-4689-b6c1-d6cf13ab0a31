package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.common.EsScrollClearReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.erp.ErpQueryProductReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.erp.ErpProductResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:45
 * @Description: 商品操作服务
 */
@FeignClient(name = "himall-trade", contextId = "ProductQueryFeign", path = "/himall-trade/product", url = "${himall-trade.dev.url:}")
public interface ProductQueryFeign {

    /**
     * 根据模板id判断是否有商品使用过该运费模板
     */
    @PostMapping(value = "/queryProductByTemplateId", consumes = "application/json")
    ResultDto<Boolean> queryProductByTemplateId(@RequestBody ProductTemplateReq template) throws TException;

    /**
     * 根据模板id查询关联商品数
     */
    @PostMapping(value = "/queryProductCountByTemplateId", consumes = "application/json")
    ResultDto<List<CountProductTemplateResp>> queryProductCountByTemplateId(@RequestBody CountProductTemplateReq template) throws TException;

    /**
     * 根据商品ID查询商品阶梯价
     */
    @PostMapping(value = "/getLadderPriceBoList", consumes = "application/json")
    ResultDto<ProductLadderPriceResp> getLadderPriceBoList(@RequestBody QueryLadderPriceReq request) throws TException;

    /**
     * 分页查询商品的集合
     */
    @PostMapping(value = "/queryProduct", consumes = "application/json")
    ResultDto<BasePageResp<ProductPageResp>> queryProduct(@RequestBody QueryProductReq request) throws TException;

    /**
     * 根据商品id查询商品集合
     */
    @PostMapping(value = "/queryProductById", consumes = "application/json")
    ResultDto<ProductListResp> queryProductById(@RequestBody QueryProductByIdReq request) throws TException;

    /**
     * 查询商品详情
     */
    @PostMapping(value = "/queryProductDetail", consumes = "application/json")
    ResultDto<ProductDetailResp> queryProductDetail(@RequestBody ProductQueryDetailReq request) throws TException;

    /**
     * 查询SKU维度的商品集合
     */
    @PostMapping(value = "/queryProductSkuMerge", consumes = "application/json")
    ResultDto<ProductSkuMergeQueryResp> queryProductSkuMerge(@RequestBody ProductSkuQueryReq request) throws TException;

    /**
     * 查询商品信息(ES构建)
     */
    @PostMapping(value = "/queryProductForEsBuild", consumes = "application/json")
    ResultDto<ProductEsResp> queryProductForEsBuild(@RequestBody QueryProductEsReq request) throws TException;

    /**
     * 查询商品信息(ERP)
     */
    @PostMapping(value = "/queryProductForErp", consumes = "application/json")
    ResultDto<BasePageResp<ErpProductResp>> queryProductForErp(@RequestBody ErpQueryProductReq request) throws TException;

    /**
     * 查询推荐商品id的集合
     */
    @PostMapping(value = "/queryRecommendProductIds", consumes = "application/json")
    ResultDto<RecommendProductIdsResp> queryRecommendProductIds(@RequestBody BaseIdReq request) throws TException;

    /**
     * 查询符合条件的商品id(最多返回1000个符合条件的id)
     */
    @PostMapping(value = "/queryProductIds", consumes = "application/json")
    ResultDto<ProductIdsResp> queryProductIds(@RequestBody QueryProductReq request) throws TException;

    /**
     * 查询商品富文本信息
     */
    @PostMapping(value = "/queryProductRichText", consumes = "application/json")
    ResultDto<ProductRichTextResp> queryProductRichText(@RequestBody QueryProductRichTextReq request) throws TException;

    /**
     * 查询商品基本信息
     */
    @PostMapping(value = "/queryProductBasic", consumes = "application/json")
    ResultDto<ProductBasicResp> queryProductBasic(@RequestBody QueryProductBasicReq request) throws TException;

    /**
     * 分页查询商品基本信息
     */
    @PostMapping(value = "/pageProductBasic", consumes = "application/json")
    ResultDto<BasePageResp<ProductBasicDto>> pageProductBasic(@RequestBody QueryProductBasicReq request) throws TException;

    /**
     * 生成商品货号
     */
    @GetMapping(value = "/generateProductCode")
    ResultDto<GenProductCodeResp> generateProductCode() throws TException;


    /**
     * 供应商首页统计商品信息
     */
    @GetMapping(value = "/querySellerStatisticalProduct")
    ResultDto<StatisticalProductResp> querySellerStatisticalProduct(@RequestParam Long shopId) throws TException;

    /**
     * 平台首页统计商品信息
     */
    @GetMapping(value = "/queryMStatisticalProduct")
    ResultDto<MStatisticalProductResp> queryMStatisticalProduct() throws TException;

    /**
     * 商家中心商品推荐
     */
    @PostMapping(value = "/queryRecommendProducts", consumes = "application/json")
    ResultDto<RecommendProductsResp> queryRecommendProducts(@RequestBody RecommendProductsReq recommendProductsReq) throws TException;

    /**
     * 根据店铺和skuId列表批量查询库存
     */
    @PostMapping(value = "/queryBatchStocks", consumes = "application/json")
    ResultDto<ProductSkuStockListResp> queryBatchStocks(@RequestBody QueryProductStockBatchParam req) throws TException;

    /**
     * 分页查询店铺sku库存
     */
    @PostMapping(value = "/queryPageStocks", consumes = "application/json")
    ResultDto<BasePageResp<ProductSkuStockResp>> queryPageStocks(@RequestBody QueryProductStockPageParam req) throws TException;

    /**
     * 查询店铺销量汇总
     */
    @PostMapping(value = "/queryShopSaleCounts", consumes = "application/json")
    ResultDto<ShopSaleCountsResp> queryShopSaleCounts(@RequestBody QueryShopSaleCountsReq request) throws TException;

    /**
     * 查询mysql商品的集合(主要用于被删除的商品查询)
     */
    @PostMapping(value = "/queryProductByIds", consumes = "application/json")
    ResultDto<List<ProductPageResp>> queryProductByIds(@RequestBody QueryProductByIdsReq request) throws TException;

    /**
     * 清空滚动id
     */
    @PostMapping(value = "/clearScrollId", consumes = "application/json")
    ResultDto<BaseResp> clearScrollId(@RequestBody EsScrollClearReq request) throws TException;
}
