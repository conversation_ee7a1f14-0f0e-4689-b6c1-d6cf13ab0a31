package com.sankuai.shangou.seashop.product.core.service.assist.event.handler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.product.core.mq.model.OrderMessage;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;

/**
 * <AUTHOR>
 * @date 2024/10/31 10:15
 */
@Component
public class CommentOrderHandler extends AbstractOrderHandler {

    @Resource
    private ProductEsBuildService productEsBuildService;

    @Override
    public void handle(OrderMessage event) {
        productEsBuildService.buildProductCommentEsByOrderId(event.getOrderId());
    }

    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.COMMENT_ORDER;
    }


}
