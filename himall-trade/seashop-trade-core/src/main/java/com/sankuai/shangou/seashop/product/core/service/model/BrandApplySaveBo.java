package com.sankuai.shangou.seashop.product.core.service.model;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/07 15:15
 */
@Getter
@Setter
@Builder
public class BrandApplySaveBo extends BaseParamReq {

    /**
     * 申请记录id
     */
    private Long id;

    /**
     * 申请类型
     */
    private BrandEnum.ApplyModeEnum applyMode;

    /**
     * 申请类型 applyMode=1 必填
     */
    private Long brandId;

    /**
     * 品牌名称 applyMode=2 必填
     */
    private String brandName;

    /**
     * logo applyMode=2 必填
     */
    private String logo;

    /**
     * 描述 applyMode=2 必填
     */
    private String description;

    /**
     * 授权证书
     */
    private List<String> authCertificateList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 店铺id
     */
    private Long shopId;

}
