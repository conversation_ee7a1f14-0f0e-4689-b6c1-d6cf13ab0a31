package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordCancelConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordConsumeReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:提供优惠券记录操作功能
 */
@FeignClient(name = "himall-trade",contextId = "CouponRecordCmdFeign", path = "/himall-trade/couponRecord", url = "${himall-trade.dev.url:}")
public interface CouponRecordCmdFeign {

    /**
     * 核销优惠券记录
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/consume", consumes = "application/json")
    ResultDto<BaseResp> consume(@RequestBody CouponRecordConsumeReq request) throws TException;

    /**
     * 撤销核销优惠券记录
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/cancelConsume", consumes = "application/json")
    ResultDto<BaseResp> cancelConsume(@RequestBody CouponRecordCancelConsumeReq request) throws TException;

}
