package com.sankuai.shangou.seashop.user.shop.excel.read.handler;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.eimport.BizType;
import com.sankuai.shangou.seashop.base.eimport.DataWrapper;
import com.sankuai.shangou.seashop.base.eimport.ImportHandler;
import com.sankuai.shangou.seashop.base.eimport.ReadResult;
import com.sankuai.shangou.seashop.base.eimport.context.ImportContextHolder;
import com.sankuai.shangou.seashop.user.common.enums.BusinessCategoryChangeEnum;
import com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService;
import com.sankuai.shangou.seashop.user.common.remote.product.model.CategoryTreeNodeBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import com.sankuai.shangou.seashop.user.shop.excel.read.dto.BusinessCategoryReadReq;
import com.sankuai.shangou.seashop.user.shop.excel.read.dto.CategoryImportContext;
import com.sankuai.shangou.seashop.user.shop.excel.read.enums.BizTypeEnum;
import com.sankuai.shangou.seashop.user.shop.excel.read.enums.CategoryLevelEnum;
import com.sankuai.shangou.seashop.user.shop.excel.read.wrapper.CategoryImportWrapper;
import com.sankuai.shangou.seashop.user.shop.mq.model.BusinessCategoryEvent;
import com.sankuai.shangou.seashop.user.shop.mq.publisher.BusinessCategoryPublisher;
import com.sankuai.shangou.seashop.user.shop.service.BusinessCategoryService;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class BusinessCategoryImportHandler extends ImportHandler<BusinessCategoryReadReq> {
    @Resource
    private RemoteCategoryService remoteCategoryService;
    @Resource
    private BusinessCategoryService businessCategoryService;
    @Resource
    private BusinessCategoryPublisher businessCategoryPublisher;

    public void check(List<CategoryTreeNodeBo> categoryBoList, List<BusinessCategoryReadReq> dataList) {

        //获取当前店铺所有一级类目
        Map<String, CategoryTreeNodeBo> firstCategoryMap = getCategoryMap(categoryBoList, CategoryLevelEnum.ONE);
        //获取当前店铺所有二级类目
        Map<String, CategoryTreeNodeBo> secondCategoryMap = getCategoryMap(categoryBoList, CategoryLevelEnum.TWO);
        //获取当前店铺所有三级类目
        Map<String, CategoryTreeNodeBo> thirdCategoryMap = getCategoryMap(categoryBoList, CategoryLevelEnum.THREE);

        for (BusinessCategoryReadReq businessCategoryReadReq : dataList) {
            // 一级类目名称
            String firstCategoryName = businessCategoryReadReq.getFirstCategoryName();
            // 二级类目名称
            String secondCategoryName = businessCategoryReadReq.getFirstCategoryName() + "," + businessCategoryReadReq.getSecondCategoryName();
            // 三级类目名称
            String thirdCategoryName = businessCategoryReadReq.getFirstCategoryName() + "," + businessCategoryReadReq.getSecondCategoryName() + "," + businessCategoryReadReq.getThirdCategoryName();
            // 分佣比例
            String commissionRate = businessCategoryReadReq.getCommissionRate();
            // 保证金
            String bond = businessCategoryReadReq.getBond();
            //校验类目
            if (StrUtil.isBlank(firstCategoryName)) {
                businessCategoryReadReq.setErrMsg("一级类目名称不能为空");
                continue;
            }
            else if (StrUtil.isBlank(secondCategoryName) && StrUtil.isNotBlank(thirdCategoryName)) {
                businessCategoryReadReq.setErrMsg("上级类目名称不能为空");
                continue;
            }
            //            获取一级类目id
            CategoryTreeNodeBo firstCategory = firstCategoryMap.get(firstCategoryName);
            if (firstCategory == null) {
                businessCategoryReadReq.setErrMsg("此类目不属于当前店铺");
                continue;
            }
            if (StrUtil.isNotBlank(businessCategoryReadReq.getSecondCategoryName())) {
                //            获取二级类目id
                CategoryTreeNodeBo secondCategory = secondCategoryMap.get(secondCategoryName);
                if (secondCategory == null) {
                    businessCategoryReadReq.setErrMsg("此类目不属于当前店铺");
                    continue;
                }
            }
            if (StrUtil.isNotBlank(businessCategoryReadReq.getThirdCategoryName())) {
                //            获取三级类目id
                CategoryTreeNodeBo thirdCategory = thirdCategoryMap.get(thirdCategoryName);
                if (thirdCategory == null) {
                    businessCategoryReadReq.setErrMsg("此类目不属于当前店铺");
                }
            }
        }
    }

    @Override
    public BizType bizType() {
        return BizTypeEnum.CATEGORY_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<BusinessCategoryReadReq> importResult) {
        CategoryImportContext categoryImportContext = ImportContextHolder.get();
        log.info("导入类目的店铺：{}", categoryImportContext.getShopId());
//        查询店铺Id的所有类目
        List<BusinessCategoryResp> categoryRespList = businessCategoryService.queryListByShopId(categoryImportContext.getShopId());
//        获取所有的类目id
        List<Long> categoryIdList = categoryRespList.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
//        根据类目id获取类目信息
        List<CategoryTreeNodeBo> categoryBoList = remoteCategoryService.queryCategoryTreeWithParent(categoryIdList);
        log.info("店铺拥有的类目信息：{}", categoryBoList);
        categoryImportContext.setCategoryBoList(categoryBoList);
        check(categoryBoList, importResult.getSuccessDataList());
    }

    private Map<String, CategoryTreeNodeBo> getCategoryMap(List<CategoryTreeNodeBo> categoryRemoteBoList, CategoryLevelEnum level) {
//        定义一个List
        List<CategoryTreeNodeBo> tempList = new ArrayList<>();
//        获取指定深度的类目
//        判断目标深度
        switch (level) {
            case ONE:
                categoryRemoteBoList = categoryRemoteBoList.stream()
                        .filter(categoryRemoteBo -> categoryRemoteBo.getDepth().equals(CategoryLevelEnum.ONE.getCode()))
                        .collect(Collectors.toList());
                break;
            case TWO:
                categoryRemoteBoList.forEach(categoryRemoteBo -> {
                    tempList.addAll(CollUtil.isEmpty(categoryRemoteBo.getChildren()) ? new ArrayList<>() : categoryRemoteBo.getChildren());
                });
                categoryRemoteBoList = tempList.stream()
                        .filter(categoryRemoteBo -> categoryRemoteBo.getDepth().equals(CategoryLevelEnum.TWO.getCode()))
                        .collect(Collectors.toList());
                break;
            case THREE:
                categoryRemoteBoList.forEach(categoryRemoteBo -> {
                    //判空
                    if (CollUtil.isNotEmpty(categoryRemoteBo.getChildren())) {
                        categoryRemoteBo.getChildren().forEach(categoryRemoteBo1 -> {
                            tempList.addAll(CollUtil.isEmpty(categoryRemoteBo1.getChildren()) ? new ArrayList<>() : categoryRemoteBo1.getChildren());
                        });
                    }
                });
                categoryRemoteBoList = tempList.stream()
                        .filter(categoryRemoteBo -> categoryRemoteBo.getDepth().equals(CategoryLevelEnum.THREE.getCode()))
                        .collect(Collectors.toList());
                break;
            default:
                break;
        }
        return categoryRemoteBoList.stream().collect(Collectors.toMap(CategoryTreeNodeBo::getFullCategoryName, categoryRemoteBo -> categoryRemoteBo));
    }

    @Override
    public void saveImportData(List<BusinessCategoryReadReq> successList) {
        CategoryImportContext categoryImportContext = ImportContextHolder.get();
        //        查询店铺Id的所有类目
        List<CategoryTreeNodeBo> categoryBoList = categoryImportContext.getCategoryBoList();
//        遍历导入成功的数据
        for (BusinessCategoryReadReq businessCategoryReadReq : successList) {
            // 一级类目名称
            String firstCategoryName = businessCategoryReadReq.getFirstCategoryName();
            // 二级类目名称
            String secondCategoryName = businessCategoryReadReq.getSecondCategoryName();
            // 三级类目名称
            String thirdCategoryName = businessCategoryReadReq.getThirdCategoryName();
            // 获取一级类目
            CategoryTreeNodeBo firstCategory = categoryBoList.stream().filter(categoryRemoteBo -> categoryRemoteBo.getName().equals(firstCategoryName)).findFirst().get();
            if (StrUtil.isBlank(secondCategoryName)) {
//            如果二级类目为空则只有一级类目
                // 获取此一级类目下的所有三级类目
                List<CategoryTreeNodeBo> thirdCategoryList = firstCategory.getChildren().stream().map(CategoryTreeNodeBo::getChildren).flatMap(List::stream).collect(Collectors.toList());

                // 获取三级类目Id
                saveCategoryList(categoryImportContext, businessCategoryReadReq, thirdCategoryList);
            } else if (StrUtil.isBlank(thirdCategoryName)) {
//              如果三级类目为空且二级类目不为空则为二级类目
                // 获取此二级类目下的所有三级类目
                List<CategoryTreeNodeBo> thirdCategoryList = firstCategory.getChildren().stream().filter(categoryRemoteBo -> categoryRemoteBo.getName().equals(secondCategoryName)).findFirst().get().getChildren();
                // 获取三级类目Id
                saveCategoryList(categoryImportContext, businessCategoryReadReq, thirdCategoryList);
            } else {
//            如果三级类目不为空则为三级类目
                // 获取此二级类目下的所有三级类目
                List<CategoryTreeNodeBo> thirdCategoryList = firstCategory
                        .getChildren().stream().filter(categoryRemoteBo -> categoryRemoteBo.getName().equals(secondCategoryName))
                        .findFirst().get()
                        .getChildren().stream().filter(categoryRemoteBo -> categoryRemoteBo.getName().equals(thirdCategoryName)).collect(Collectors.toList());
                // 获取三级类目Id
                saveCategoryList(categoryImportContext, businessCategoryReadReq, thirdCategoryList);
            }
        }
    }

    private void saveCategoryList(CategoryImportContext categoryImportContext, BusinessCategoryReadReq businessCategoryReadReq, List<CategoryTreeNodeBo> thirdCategoryList) {
        List<Long> thirdCategoryIds = thirdCategoryList.stream().map(CategoryTreeNodeBo::getId).collect(Collectors.toList());
//                查询店铺已有类目
        List<BusinessCategory> businessCategoryList = businessCategoryService.queryListByShopIdAndCategoryIds(categoryImportContext.getShopId(), thirdCategoryIds);
        // 如果店铺已有类目为空
        if (CollUtil.isEmpty(businessCategoryList)) {
            return;
        }
        businessCategoryList.forEach(businessCategory -> {
            if (StrUtil.isNotBlank(businessCategoryReadReq.getCommissionRate())) {
                businessCategory.setCommissionRate(new BigDecimal(businessCategoryReadReq.getCommissionRate()));
            }
            if (StrUtil.isNotBlank(businessCategoryReadReq.getBond())) {
                businessCategory.setBond(new BigDecimal(businessCategoryReadReq.getBond()));
            }
        });
        businessCategoryService.updateBatchById(businessCategoryList);

        // 发送经营类目变动事件
        List<Long> categoryIds = businessCategoryList.stream().map(BusinessCategory::getCategoryId).collect(Collectors.toList());
        BusinessCategoryEvent event = BusinessCategoryEvent.of(categoryImportContext.getShopId(), categoryIds,
                BusinessCategoryChangeEnum.EDIT.getCode());
        businessCategoryPublisher.sendEvent(event);
    }

    @Override
    public DataWrapper<BusinessCategoryReadReq> wrapData(List<BusinessCategoryReadReq> errList) {
        return new CategoryImportWrapper(errList);
    }
}