package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "前端显示限时购活动请求对象")
public class FlashSaleShowReq extends BaseParamReq {

    @Schema(description = "活动ID", required = true)
    @PrimaryField(title = "活动ID")
    private Long id;

    @Schema(description = "是否前端显示", required = true)
    @ExaminField(description = "是否前端显示")
    private Boolean frontFlag;

    @Override
    public void checkParameter() {
        if (id == null || id <= 0) {
            throw new InvalidParamException("活动ID不能为空");
        }
        if (frontFlag == null) {
            throw new InvalidParamException("是否前端显示不能为空");
        }
    }


}
