package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class CouponRecordQueryBo extends BasePageReq {

    //用户ID
    private Long userId;

    //店铺ID
    private Long shopId;

    //优惠券活动ID
    private Long couponId;

    //状态 0-未使用 1-已使用 2-已过期
    private Integer status;

    //店铺名称
    private String shopName;

    //优惠券名称
    private String couponName;

    //订单ID
    private String orderId;

    //领用人账号
    private String userName;

    //领取时间-开始
    private Date startReceiveTime;

    //领取时间-结束
    private Date endReceiveTime;

    //使用时间-开始
    private Date startUseTime;

    //使用时间-结束
    private Date endUseTime;

    //按价值倒序排序
    private Boolean orderByPriceDesc;

    // 根据状态自动排序
    private Boolean autoOrderByStatus;

}
