package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.SkuAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecValueMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * sku 查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:27
 */
@Component
@Slf4j
public class QuerySkuHandler extends AbsProductHandler {

    @Resource
    private SkuAssist skuAssist;
    @Resource
    private SpecValueMapper specValueMapper;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();
        ProductBo productBo = context.getOldProductBo();

        // 查询sku集合
        List<ProductSkuBo> skuList = skuAssist.getSkuBoList(productId);
        productBo.setSkuList(skuList);
        productBo.setStock(skuAssist.getSumStock(productBo.getSkuList()));
        productBo.setSafeStock(skuAssist.getFirstSafeStock(productBo.getSkuList()));

        if (CollectionUtils.isNotEmpty(skuList)) {
            productBo.setSkuAutoId(skuList.get(0).getSkuAutoId());
        }
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_SKU;
    }

}
