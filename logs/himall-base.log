2025-07-30 11:55:14.178 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 11:55:14.343 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [55] -| Starting BaseApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 15428 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-30 11:55:14.343 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-30 11:55:14.344 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-07-30 11:55:14.852 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-30 11:55:14.853 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-30 11:55:21.705 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 11:55:21.708 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 11:55:21.864 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-30 11:55:27.515 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 11:55:27.611 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-30 11:55:28.135 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-30 11:55:28.891 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-30 11:55:28.897 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-30 11:55:29.267 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-30 11:55:29.268 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-30 11:55:29.268 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-30 11:55:29.268 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-30 11:55:32.082 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@********'
2025-07-30 11:55:32.696 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-30 11:55:32.888 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-30 11:55:32.903 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-30 11:55:32.921 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-30 11:55:32.939 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-30 11:55:32.960 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-30 11:55:32.973 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-30 11:55:33.014 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-30 11:55:33.024 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-30 11:55:33.059 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-30 11:55:33.069 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-30 11:55:33.078 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-30 11:55:33.099 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 11:55:33.116 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.SelectById [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 11:55:33.118 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-30 11:55:33.131 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-30 11:55:33.141 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-30 11:55:33.153 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-30 11:55:33.165 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-30 11:55:33.173 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-30 11:55:33.198 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-30 11:55:33.216 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-30 11:55:33.232 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-30 11:55:33.248 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-30 11:55:33.266 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-30 11:55:33.282 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-30 11:55:33.299 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-30 11:55:33.315 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-30 11:55:33.336 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-30 11:55:33.355 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-30 11:55:33.378 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-30 11:55:33.401 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-30 11:55:33.425 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-30 11:55:33.450 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-30 11:55:33.473 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-30 11:55:33.511 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-30 11:55:33.533 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-30 11:55:33.565 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-30 11:55:33.589 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-30 11:55:33.621 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-30 11:55:33.644 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-30 11:55:33.660 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-30 11:55:33.679 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-30 11:55:33.700 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-30 11:55:33.707 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-30 11:55:33.712 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-30 11:55:33.717 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-30 11:55:33.723 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-30 11:55:33.734 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-30 11:55:33.753 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-30 11:55:33.775 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-30 11:55:33.791 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-30 11:55:33.814 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-30 11:55:33.837 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-30 11:55:33.863 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-30 11:55:33.883 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-30 11:55:33.910 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-30 11:55:33.932 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-30 11:55:33.951 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-30 11:55:33.975 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-30 11:55:33.999 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-30 11:55:34.033 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-30 11:55:34.068 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-30 11:55:34.097 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-30 11:55:34.117 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-30 11:55:34.158 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-30 11:55:34.183 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-30 11:55:34.207 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-30 11:55:34.230 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-30 11:55:34.236 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-30 11:55:34.256 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-30 11:55:34.274 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-07-30 11:55:34.276 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-07-30 11:55:35.901 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-07-30 11:55:35.925 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:18
2025-07-30 11:55:36.245 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-30 11:55:42.457 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-30 11:55:52.583 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-30 11:55:53.955 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-30 11:55:55.732 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-30 11:55:56.025 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:55:58.237 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:05.441 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:05.441 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 11:56:07.057 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-30 11:56:08.995 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 11:56:08.995 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 11:56:09.451 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-30 11:56:09.658 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-07-30 11:56:10.037 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-30 11:56:10.097 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-30 11:56:10.781 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-30 11:56:10.781 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-07-30 11:56:10.803 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-30 11:56:10.804 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-30 11:56:16.445 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-30 11:56:16.911 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-30 11:56:17.288 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-30 11:56:17.380 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:19.696 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:27.435 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:27.435 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 11:56:27.444 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:29.892 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:36.840 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:36.840 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 11:56:36.848 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:39.432 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:47.454 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:47.454 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-30 11:56:47.463 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:50.368 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:00.147 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:00.147 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-30 11:57:01.298 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:03.911 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:11.607 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:11.607 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-30 11:57:11.626 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:14.212 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:22.221 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:22.221 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-30 11:57:22.234 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:24.710 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:32.474 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:32.474 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-30 11:57:32.482 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:35.023 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:42.513 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:42.513 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-30 11:57:42.523 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:44.615 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:52.645 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:52.646 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-30 11:57:52.653 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:55.092 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:02.877 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:02.877 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-30 11:58:02.885 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:05.468 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:12.760 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:12.760 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-30 11:58:12.769 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:15.137 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:24.061 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:24.062 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-30 11:58:24.070 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:26.305 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:34.637 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:34.637 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-30 11:58:34.647 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:37.116 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:45.726 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:45.726 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-30 11:58:53.051 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-30 11:58:53.068 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-30 11:58:54.766 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:15428, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-30 11:58:55.706 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=441, lastTimeStamp=1753847934774}] - instanceId:[InstanceId{instanceId=*******:15428, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-30 11:58:55.921 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-30 11:58:55.925 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 11:58:55.925 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 11:58:55.925 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-30 11:58:59.347 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-30 11:59:03.652 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:syncRegionData, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1f739627[class com.sankuai.shangou.seashop.base.core.task.RegionTask#syncRegionData]
2025-07-30 11:59:03.652 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2a4d39fa[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-30 11:59:03.652 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e15cac4[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-30 11:59:03.652 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3d150d83[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-30 11:59:03.653 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3ed476a8[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-30 11:59:03.694 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d950a61[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-30 11:59:03.694 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2d06acfd[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-30 11:59:03.694 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@386ad5cb[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-30 11:59:10.401 |-ERROR [Thread-458][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 11:59:10.598 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-30 11:59:10.683 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-30 11:59:10.720 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 11:59:10.930 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-30 11:59:11.146 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [61] -| Started BaseApplication in 244.207 seconds (JVM running for 247.988)
2025-07-30 11:59:11.564 |-INFO  [task-14][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 11:59:11.564 |-INFO  [task-14][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 11:59:12.890 |-INFO  [task-14][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-30 11:59:16.183 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-30 11:59:16.187 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-30 11:59:16.192 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [32] -| 服务启动成功！
2025-07-30 11:59:16.306 |-INFO  [task-12][6dd660225b612302] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-30 11:59:16.308 |-INFO  [task-12][6dd660225b612302] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-30 11:59:16.722 |-ERROR [task-15][c31e66577ef0d386] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-30 11:59:16.770 |-INFO  [task-15][c31e66577ef0d386] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 11:59:18.051 |-INFO  [RMI TCP Connection(19)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:59:20.061 |-WARN  [RMI TCP Connection(17)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-30 13:37:23.924 |-INFO  [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 13:37:25.160 |-INFO  [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 13:38:33.624 |-INFO  [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 13:38:33.707 |-INFO  [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 13:42:48.031 |-INFO  [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 13:43:14.206 |-INFO  [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 13:44:11.525 |-INFO  [XNIO-1 task-1][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 13:44:16.128 |-INFO  [XNIO-1 task-1][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 13:46:02.125 |-INFO  [XNIO-1 task-1][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 13:46:02.206 |-INFO  [XNIO-1 task-1][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 13:46:43.167 |-INFO  [XNIO-1 task-1][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 13:46:43.257 |-INFO  [XNIO-1 task-1][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:03:20.282 |-INFO  [XNIO-1 task-1][d0f668018217a811] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:03:20.427 |-INFO  [XNIO-1 task-1][d0f668018217a811] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:17:38.065 |-INFO  [XNIO-1 task-1][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:17:38.474 |-INFO  [XNIO-1 task-1][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:28:58.984 |-INFO  [XNIO-1 task-1][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:28:59.417 |-INFO  [XNIO-1 task-1][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:35:34.883 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:35:34.972 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:35:40.585 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 14:35:40.908 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 14:35:40.975 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 14:35:41.436 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 14:35:41.629 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:35:41.713 |-INFO  [XNIO-1 task-1][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:36:34.837 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:36:34.951 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:39:06.705 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 14:39:06.776 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 14:39:07.750 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 14:39:08.229 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 14:39:08.374 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:39:08.483 |-INFO  [XNIO-1 task-1][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:39:11.806 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-30 14:39:11.806 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 14:39:11.806 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 14:39:11.807 |-INFO  [Thread-424][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:11.806 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 14:39:11.807 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.807 |-INFO  [Thread-425][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.811 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-30 14:39:11.832 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=441, lastTimeStamp=1753857551832}] instanceId:[InstanceId{instanceId=*******:15428, stable=false}] @ namespace:[himall-base].
2025-07-30 14:39:11.863 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:53:01.146 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 14:53:01.365 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [55] -| Starting BaseApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 25168 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-30 14:53:01.366 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-30 14:53:01.367 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-07-30 14:53:01.939 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-30 14:53:01.939 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-30 14:53:08.166 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 14:53:08.167 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 14:53:08.292 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-30 14:53:13.716 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 14:53:13.812 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-30 14:53:14.396 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-30 14:53:15.228 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-30 14:53:15.236 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-30 14:53:15.431 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-30 14:53:15.433 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-30 14:53:15.433 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-30 14:53:15.433 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-30 14:53:18.007 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@497f8a4b'
2025-07-30 14:53:18.438 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BanksMapper.xml]'
2025-07-30 14:53:18.573 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseAgreementMapper.xml]'
2025-07-30 14:53:18.585 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleCategoryMapper.xml]'
2025-07-30 14:53:18.600 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseArticleMapper.xml]'
2025-07-30 14:53:18.627 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormFieldMapper.xml]'
2025-07-30 14:53:18.644 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseCustomFormMapper.xml]'
2025-07-30 14:53:18.653 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMessageNoticeSettingMapper.xml]'
2025-07-30 14:53:18.683 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseMobileFootMenuMapper.xml]'
2025-07-30 14:53:18.692 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseModuleProductMapper.xml]'
2025-07-30 14:53:18.720 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseOperationLogMapper.xml]'
2025-07-30 14:53:18.728 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceCategoryMapper.xml]'
2025-07-30 14:53:18.740 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BasePhotoSpaceMapper.xml]'
2025-07-30 14:53:18.760 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.Insert [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-30 14:53:18.776 |-WARN  [main][] -  com.baomidou.mybatisplus.core.injector.methods.SelectById [411] -| [com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-30 14:53:18.780 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseRegionMapper.xml]'
2025-07-30 14:53:18.791 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSettledMapper.xml]'
2025-07-30 14:53:18.801 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseSiteSettingMapper.xml]'
2025-07-30 14:53:18.813 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTemplatePageMapper.xml]'
2025-07-30 14:53:18.824 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicMapper.xml]'
2025-07-30 14:53:18.833 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseTopicModuleMapper.xml]'
2025-07-30 14:53:18.862 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWXMenuMapper.xml]'
2025-07-30 14:53:18.886 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\BaseWeixinMsgTemplateMapper.xml]'
2025-07-30 14:53:18.908 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\ExpressCompanyMapper.xml]'
2025-07-30 14:53:18.928 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\MemberOpenIdMapper.xml]'
2025-07-30 14:53:18.957 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\PlatformTaskInfoMapper.xml]'
2025-07-30 14:53:18.979 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\RefundReasonMapper.xml]'
2025-07-30 14:53:19.005 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SellerTaskInfoMapper.xml]'
2025-07-30 14:53:19.026 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordCouponMapper.xml]'
2025-07-30 14:53:19.048 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SendMessageRecordMapper.xml]'
2025-07-30 14:53:19.072 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\SlideAdMapper.xml]'
2025-07-30 14:53:19.094 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\WxAppletFormDataMapper.xml]'
2025-07-30 14:53:19.121 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteMapper.xml]'
2025-07-30 14:53:19.149 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\FavoriteShopMapper.xml]'
2025-07-30 14:53:19.184 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\InvoiceTitleMapper.xml]'
2025-07-30 14:53:19.206 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\LabelMapper.xml]'
2025-07-30 14:53:19.235 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ManagerMapper.xml]'
2025-07-30 14:53:19.260 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberBuyCategoryMapper.xml]'
2025-07-30 14:53:19.285 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberContactMapper.xml]'
2025-07-30 14:53:19.310 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberLabelMapper.xml]'
2025-07-30 14:53:19.346 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\MemberMapper.xml]'
2025-07-30 14:53:19.371 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\PrivilegeMapper.xml]'
2025-07-30 14:53:19.395 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RoleMapper.xml]'
2025-07-30 14:53:19.415 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\RolePrivilegeMapper.xml]'
2025-07-30 14:53:19.438 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ShippingAddressMapper.xml]'
2025-07-30 14:53:19.445 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\FavoriteShopExtMapper.xml]'
2025-07-30 14:53:19.454 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\LabelExtMapper.xml]'
2025-07-30 14:53:19.460 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\ManagerExtMapper.xml]'
2025-07-30 14:53:19.468 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberContactExtMapper.xml]'
2025-07-30 14:53:19.482 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\account\ext\MemberExtMapper.xml]'
2025-07-30 14:53:19.505 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyDetailMapper.xml]'
2025-07-30 14:53:19.530 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryApplyMapper.xml]'
2025-07-30 14:53:19.553 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryFormMapper.xml]'
2025-07-30 14:53:19.577 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\BusinessCategoryMapper.xml]'
2025-07-30 14:53:19.602 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\CustomerServiceMapper.xml]'
2025-07-30 14:53:19.633 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaContentMapper.xml]'
2025-07-30 14:53:19.657 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightAreaDetailMapper.xml]'
2025-07-30 14:53:19.689 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\FreightTemplateMapper.xml]'
2025-07-30 14:53:19.714 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\OrderSettingMapper.xml]'
2025-07-30 14:53:19.739 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\RestrictedAreaMapper.xml]'
2025-07-30 14:53:19.767 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeGroupMapper.xml]'
2025-07-30 14:53:19.794 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShippingFreeRegionMapper.xml]'
2025-07-30 14:53:19.823 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopErpMapper.xml]'
2025-07-30 14:53:19.865 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopExtMapper.xml]'
2025-07-30 14:53:19.896 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopFreeShippingAreaMapper.xml]'
2025-07-30 14:53:19.920 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopInvoiceConfigMapper.xml]'
2025-07-30 14:53:19.977 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopMapper.xml]'
2025-07-30 14:53:20.001 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopOpenApiSettingMapper.xml]'
2025-07-30 14:53:20.029 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ShopShipperMapper.xml]'
2025-07-30 14:53:20.048 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryExtMapper.xml]'
2025-07-30 14:53:20.053 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\BusinessCategoryFormExtMapper.xml]'
2025-07-30 14:53:20.070 |-DEBUG [main][] -  com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean [49] -| Parsed mapper file: 'file [E:\work\himallWork\himall-base\seashop-base-dao\target\classes\mapper\user\shop\ext\ShopManagerMapper.xml]'
2025-07-30 14:53:20.077 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [150] -| Use localhost address 
2025-07-30 14:53:20.078 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [155] -| Get DESKTOP-GBHOUCA/******* network interface 
2025-07-30 14:53:20.890 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [159] -| Get network interface info: name:net0 (Sangfor aTrust VNIC)
2025-07-30 14:53:20.915 |-DEBUG [main][] -  com.baomidou.mybatisplus.core.toolkit.Sequence [103] -| Initialization Sequence datacenterId:15 workerId:12
2025-07-30 14:53:21.127 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = **************:9876
2025-07-30 14:53:27.790 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv **************:9876
2025-07-30 14:53:38.529 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-30 14:53:40.006 |-INFO  [redisson-netty-2-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for **************/**************:6379
2025-07-30 14:53:41.905 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for **************/**************:6379
2025-07-30 14:53:42.224 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:53:45.694 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:53:55.562 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:53:55.562 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 14:53:57.119 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-30 14:53:59.134 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 14:53:59.134 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 14:53:59.628 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-30 14:53:59.850 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinMpAppId(String), weixinMpAppSecret(String)
2025-07-30 14:54:00.249 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-30 14:54:00.317 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-30 14:54:01.098 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==>  Preparing: select id, `key`, `value` from base_site_setting WHERE ( `key` in ( ? , ? ) )
2025-07-30 14:54:01.100 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| ==> Parameters: weixinAppletId(String), weixinAppletSecret(String)
2025-07-30 14:54:01.129 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper.selectByExample [135] -| <==      Total: 2
2025-07-30 14:54:01.130 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx5a538cdb4b7b286d,wxAppSecret:8517959f6e6dc5537995ae4a643d3649
2025-07-30 14:54:07.449 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-30 14:54:07.909 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-30 14:54:08.316 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-30 14:54:08.411 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:11.092 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:54:20.685 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:54:20.686 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 14:54:20.696 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:23.854 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:54:35.805 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:54:35.806 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 14:54:35.818 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:38.984 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:54:48.396 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:54:48.396 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-30 14:54:48.407 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:51.479 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:03.369 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:03.369 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-30 14:55:04.596 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:07.303 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:15.316 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:15.316 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-30 14:55:15.335 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:17.969 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:26.148 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:26.149 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-30 14:55:26.160 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:28.915 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:37.229 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:37.229 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-30 14:55:37.239 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:40.050 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:47.789 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:47.789 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-30 14:55:47.802 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:50.569 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:58.618 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:58.618 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-30 14:55:58.628 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:01.189 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:08.605 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:08.605 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-30 14:56:08.614 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:11.360 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:21.397 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:21.399 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-30 14:56:21.407 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:23.757 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:30.869 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:30.870 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-30 14:56:30.878 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:33.075 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:39.925 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:39.925 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-30 14:56:39.933 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:42.411 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:48.835 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:48.835 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-30 14:56:57.086 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-30 14:56:57.105 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-30 14:56:58.959 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:25168, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-30 14:56:59.989 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=441, lastTimeStamp=1753858618967}] - instanceId:[InstanceId{instanceId=*******:25168, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-30 14:57:00.169 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-30 14:57:00.172 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 14:57:00.172 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 14:57:00.172 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-30 14:57:03.226 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-30 14:57:07.241 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:syncRegionData, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1fb89238[class com.sankuai.shangou.seashop.base.core.task.RegionTask#syncRegionData]
2025-07-30 14:57:07.242 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5f18a74d[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-30 14:57:07.242 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4eddb903[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-30 14:57:07.242 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2546604e[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-30 14:57:07.242 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7e341f8c[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-30 14:57:07.279 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@648cfdc8[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-30 14:57:07.280 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47ba55fb[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-30 14:57:07.280 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@51e81952[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-30 14:57:13.563 |-ERROR [Thread-504][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:57:13.752 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-30 14:57:13.842 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-30 14:57:13.881 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 14:57:14.086 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-30 14:57:14.310 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [61] -| Started BaseApplication in 263.58 seconds (JVM running for 268.291)
2025-07-30 14:57:14.711 |-INFO  [task-15][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:57:14.711 |-INFO  [task-15][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:57:15.084 |-INFO  [task-15][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-30 14:57:18.463 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [44] -|   ...  DDL start create  ...  
2025-07-30 14:57:18.467 |-DEBUG [main][] -  com.baomidou.mybatisplus.autoconfigure.DdlApplicationRunner [53] -|   ...  DDL end create  ...  
2025-07-30 14:57:18.470 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [32] -| 服务启动成功！
2025-07-30 14:57:18.582 |-INFO  [task-12][e3953bdd72653197] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-30 14:57:18.584 |-INFO  [task-12][e3953bdd72653197] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-30 14:57:18.903 |-ERROR [task-11][370dac317abac0ce] -  com.hishop.xxljob.client.boot.service.impl.JobGroupServiceImpl [92] -| 自动注册执行器失败,appName=sss
2025-07-30 14:57:18.929 |-INFO  [task-11][370dac317abac0ce] -  com.hishop.xxljob.client.boot.core.XxlJobAutoRegister [58] -| auto register xxl-job error!
java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:653) ~[?:1.8.0_121]
	at java.util.ArrayList.get(ArrayList.java:429) ~[?:1.8.0_121]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.addJobInfo(XxlJobAutoRegister.java:73) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:56) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at com.hishop.xxljob.client.boot.core.XxlJobAutoRegister.onApplicationEvent(XxlJobAutoRegister.java:31) ~[hishop-xxl-job-client-boot-starter-2.0.1-20250708.013100-19.jar:2.0.1-SNAPSHOT]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.context.event.SimpleApplicationEventMulticaster.lambda$multicastEvent$0(SimpleApplicationEventMulticaster.java:142) ~[spring-context-5.3.33.jar:5.3.33]
	at org.springframework.cloud.sleuth.instrument.async.TraceRunnable.run(TraceRunnable.java:64) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142) [?:1.8.0_121]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617) [?:1.8.0_121]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:57:20.581 |-INFO  [RMI TCP Connection(12)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:57:21.706 |-WARN  [RMI TCP Connection(10)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-30 14:59:08.967 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:59:10.094 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 14:59:15.088 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 14:59:15.359 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 14:59:15.425 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 14:59:16.121 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 14:59:16.309 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 14:59:16.399 |-INFO  [XNIO-1 task-1][a4a0d3cc07cd9e32] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:00:18.978 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:00:19.072 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:00:19.380 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:00:19.414 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:00:19.418 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 15:00:19.594 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 15:00:19.761 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:00:19.834 |-INFO  [XNIO-1 task-1][1a2074e9a050586c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:01:03.618 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:01:03.710 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:01:04.077 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:01:04.112 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:01:04.115 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 15:01:04.225 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 15:01:04.401 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:01:04.463 |-INFO  [XNIO-1 task-1][af524afd2e31fc19] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:01:59.788 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:01:59.879 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:02:01.551 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:02:01.613 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:02:01.616 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 15:02:01.737 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 15:02:01.900 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:02:01.967 |-INFO  [XNIO-1 task-1][2ee05756fb707a7a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:07:49.496 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:07:49.871 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:07:55.437 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:07:55.502 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:07:55.567 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| create 请求入参. request={"shopId":0,"client":"header","type":19,"currentShopId":null}
2025-07-30 15:07:56.058 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| create success. 请求结果. response={"data":{"page":"{}"},"code":0,"message":null}
2025-07-30 15:07:56.227 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryFreightTemplateList 请求入参. request={"shopId":162,"templateId":null,"templateNames":null,"valuationMethod":null,"valuationMethods":null}
2025-07-30 15:07:56.316 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryFreightTemplateList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":210,"operationUserId":null,"operationShopId":0},{"id":168,"name":"包邮","sourceAddress":4,"sendTime":"0","whetherFree":1,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1750296914000,"updateTime":1750296914000,"productNum":43,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:08:05.722 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:05.785 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:08:06.048 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[170]}
2025-07-30 15:08:06.049 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[170],"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:06.082 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:08:08.081 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:08.166 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:08:08.320 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[170]}
2025-07-30 15:08:08.321 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[170],"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:08.358 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:08:08.808 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:08.840 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:08:08.982 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[170]}
2025-07-30 15:08:08.982 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[170],"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:09.014 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:08:10.378 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:10.440 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:08:10.570 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[170]}
2025-07-30 15:08:10.571 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[170],"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:10.602 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:08:11.044 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| getShopList 请求入参. request={"shopIds":[162],"userId":null,"sortList":null,"pageSize":10,"pageNo":1,"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:11.076 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| getShopList success. 请求结果. response={"data":{"shopRespList":[{"id":162,"gradeId":0,"shopName":"官方自营店","shopStatus":7,"shopStatusDesc":null,"adapayStatus":10,"adapayStatusDesc":null,"plateStatus":7,"plateStatusDesc":null,"updateAdaPayStatus":null,"updateAdaPayStatusDesc":null,"createDate":*************,"endDate":null,"contactsName":"婚庆商城","contactsPhone":"***********","contactsEmail":"<EMAIL>","bankAccountName":"456","bankAccountNumber":"pI8twLEQVIHUS7Be0KG6oA==","bankName":"工商银行","bankCode":"********","bankRegionId":1100,"bankType":1,"senderName":null,"senderAddress":null,"senderPhone":null,"whetherPayBond":false,"whetherAgreement":false,"whetherSupply":true,"whetherAgainSign":null,"whetherSelf":true,"businessType":1,"shopAccount":null,"shopType":2,"shopTypeDesc":null,"fullCategoryName":null,"whetherOpenExclusiveMember":false,"adaMemberId":null,"serialNumber":7,"categoryIds":null}]},"code":0,"message":null}
2025-07-30 15:08:11.205 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.user.shop.thrift.impl.FreightAreaQueryController [80] -| 【运费模版】查询运费模版列表，参数：{"operationShopId":0,"id":[170]}
2025-07-30 15:08:11.206 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| queryByTemplateIdList 请求入参. request={"id":[170],"operationUserId":null,"operationShopId":0}
2025-07-30 15:08:11.235 |-INFO  [XNIO-1 task-1][1e0d26f3c500cf0b] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| queryByTemplateIdList success. 请求结果. response={"data":{"result":[{"id":170,"name":"10元运费","sourceAddress":9118,"sendTime":"12","whetherFree":0,"valuationMethod":0,"shippingMethod":null,"shopId":162,"nonSalesAreaHide":false,"createTime":1751422791000,"updateTime":1751422791000,"productNum":null,"operationUserId":null,"operationShopId":0}]},"code":0,"message":null}
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 15:27:59.580 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 15:27:59.580 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-473][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-473][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.580 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.581 |-INFO  [Thread-474][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:27:59.583 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-30 15:27:59.587 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=441, lastTimeStamp=1753860479587}] instanceId:[InstanceId{instanceId=*******:25168, stable=false}] @ namespace:[himall-base].
2025-07-30 15:27:59.623 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:28:39.585 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-30 15:28:39.631 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 15:28:39.656 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:28:39.673 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 15:28:39.673 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-30 15:28:39.674 |-INFO  [Thread-503][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-30 15:28:39.675 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-30 15:28:39.753 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-30 15:28:40.574 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_shop_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.574 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_shop_brand_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.574 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.575 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.575 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.575 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_user_exclusive_member_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.576 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_custom_form_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.577 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_product_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.577 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_business_category_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.577 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_corp_member_audit_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.578 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.578 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.578 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.578 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.633 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_chengpei_local', namespace='', nameServer='**************:9876', topic='seashop_operation_log_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:43.903 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-30 15:28:43.915 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-30 15:28:43.918 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-30 15:28:43.918 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-30 15:28:43.918 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-30 15:28:43.922 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-30 15:28:43.922 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-30 15:28:43.922 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
