package com.sankuai.shangou.seashop.product.core.service.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/26 15:22
 */
@Getter
@Setter
public class CategoryCacheBo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 是否有子类目
     */
    private Boolean hasChildren;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 是否显示
     */
    private Boolean whetherShow;

    /**
     * 自定义表单Id
     */
    private Long customFormId;
}
