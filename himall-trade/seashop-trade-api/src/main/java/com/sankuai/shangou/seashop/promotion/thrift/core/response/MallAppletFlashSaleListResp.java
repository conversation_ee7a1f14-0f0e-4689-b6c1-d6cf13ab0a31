package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/17 19:06
 */
@Schema(description = "限时购活动响应体")
@Data
public class MallAppletFlashSaleListResp extends BaseThriftDto {

    @Schema(description = "限时购活动响应体列表")
    private List<MallAppletFlashSaleResp> respList;


}
