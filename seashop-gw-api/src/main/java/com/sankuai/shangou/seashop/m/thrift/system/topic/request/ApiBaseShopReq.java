package com.sankuai.shangou.seashop.m.thrift.system.topic.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

@TypeDoc(description = "通用请求对象，适用于id与店铺id做联合主键的一些通用场景，如删除、获取等")
@Data
public class ApiBaseShopReq extends BaseParamReq {

    @FieldDoc(description = "id")
    @ExaminField(description = "id")
    private Long id;
    @FieldDoc(description = "店铺id")
    private Long shopId;

    public void checkParameter() {
        if (this.shopId == null) {
            throw new IllegalArgumentException("店铺id不能为空");
        }

    }


}
