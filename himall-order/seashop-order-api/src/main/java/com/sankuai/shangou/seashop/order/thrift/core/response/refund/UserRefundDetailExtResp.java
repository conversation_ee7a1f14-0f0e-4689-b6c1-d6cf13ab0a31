package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/26/026
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "退款明细扩展对象")
public class UserRefundDetailExtResp extends BaseThriftDto {

    @FieldDoc(description = "退款明细")
    private UserRefundDetailResp detail;

    @FieldDoc(description = "退款商品列表")
    private List<UserRefundProductDto> productList;


}
