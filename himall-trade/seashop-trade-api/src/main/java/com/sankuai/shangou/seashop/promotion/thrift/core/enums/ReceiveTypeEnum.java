package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

/**
 * @author: l<PERSON><PERSON>
 * @date: 2023/11/8/008
 * @description:
 */
public enum ReceiveTypeEnum {

    SHOP_INDEX(0, "店铺首页"),
    ACTIVE(2, "主动发放"),
    ;
    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    ReceiveTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
