package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 15:59
 */
public class CollocationEnum {

    public enum CollocationStatusEnum {

        NOT_BEGIN(0, "未开始"),

        ONGOING(1, "进行中"),

        ENDED(-1, "已结束"),
        ;

        private final Integer code;
        private final String desc;

        CollocationStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
