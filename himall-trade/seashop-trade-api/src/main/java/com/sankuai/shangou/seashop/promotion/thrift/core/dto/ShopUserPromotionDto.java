package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "店铺对于用户的营销对象")
public class ShopUserPromotionDto extends BaseThriftDto {

    @Schema(description = "店铺ID")
    private Long shopId;
    // 同一个时间点，可以有多个折扣活动，每个活动配置不同的商品，
    @Schema(description = "店铺折扣对象")
    private List<ShopDiscountDto> shopDiscountList;
    @Schema(description = "店铺满减活动对象")
    private ShopReductionDto shopReduction;
    // 同一个时间点，可以有多个专享价活动，每个活动配置不同的商品，
    @Schema(description = "店铺专享价列表")
    private List<ShopExclusivePriceDto> shopExclusivePriceList;


}
