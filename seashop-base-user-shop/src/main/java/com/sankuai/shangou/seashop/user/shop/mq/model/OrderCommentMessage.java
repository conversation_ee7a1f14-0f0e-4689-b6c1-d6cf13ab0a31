package com.sankuai.shangou.seashop.user.shop.mq.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: lhx
 * @date: 2024/1/4/004
 * @description:
 */
@NoArgsConstructor
@Getter
@Setter
@ToString
public class OrderCommentMessage implements Serializable {

    /**
     * 主键
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 店铺id
     */
    @JsonProperty("shop_id")
    private Long shopId;
}