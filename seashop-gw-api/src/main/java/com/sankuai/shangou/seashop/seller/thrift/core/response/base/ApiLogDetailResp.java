package com.sankuai.shangou.seashop.seller.thrift.core.response.base;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/1 15:34
 */
@Data
@TypeDoc(description = "日志详情")
public class ApiLogDetailResp extends BaseParamReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @FieldDoc(description = "操作人账号")
    private String operationUserAccount;

    @Schema(description = "操作人名称")
    private String operationUserName;

    @FieldDoc(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    @FieldDoc(description = "操作类型名称")
    private String operationName;

    @FieldDoc(description = "字段变化详情")
    private String items;


    public Long getOperationTimeLong() {
        return this.date2Long(this.operationTime);
    }


    public void setOperationTimeLong(Long operationTime) {
        this.operationTime = this.long2Date(operationTime);
    }


}
