package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品品牌查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryBrandHandler extends AbsProductHandler {

    @Resource
    private BrandRepository brandRepository;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        ProductBo productBo = context.getOldProductBo();
        Brand brand = brandRepository.getById(productBo.getBrandId());
        productBo.setBrandId(null);
        if (brand != null) {
            productBo.setBrandId(brand.getId());
            productBo.setBrandName(brand.getName());
            productBo.setBrandLogo(brand.getLogo());
        }
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_BRAND;
    }

}
