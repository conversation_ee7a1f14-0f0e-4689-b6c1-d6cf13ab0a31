package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "专享价活动商品修改对象")
public class ExclusivePriceProductUpdateReq extends BaseParamReq {

    @Schema(description = "主键ID", required = true)
    private Long id;

    @Schema(description = "供应商Id", required = true)
    private Long shopId;

    @Schema(description = "商品列表", required = true)
    private List<ExclusivePriceProductDto> productList;

    @Override
    public void checkParameter() {
        if (this.id == null) {
            throw new InvalidParamException("id不能为空");
        }
        if (this.shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (CollUtil.isEmpty(this.productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
