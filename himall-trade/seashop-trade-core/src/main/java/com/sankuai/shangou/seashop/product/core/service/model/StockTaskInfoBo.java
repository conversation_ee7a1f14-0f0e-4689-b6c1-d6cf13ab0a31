package com.sankuai.shangou.seashop.product.core.service.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/18 14:58
 */
@Setter
@Getter
public class StockTaskInfoBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 库存变更任务id
     */
    private Long taskId;

    /**
     * 执行状态 0-待执行 1-执行中 2-执行成功 3-执行失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errorReason;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * 商品ID_规格1ID_规格2ID_规格3ID
     */
    private String skuId;

    /**
     * 货号
     */
    private String skuCode;

    /**
     * 任务提交的库存, 变动库存/覆盖库存
     */
    private Long stock;

    /**
     * 修改前的库存
     */
    private Long beforeStock;

    /**
     * 增加/减少的库存
     */
    private Long changeStock;

    /**
     * 修改后的库存
     */
    private Long afterStock;

    /**
     * 店铺id
     */
    private Long shopId;
}
