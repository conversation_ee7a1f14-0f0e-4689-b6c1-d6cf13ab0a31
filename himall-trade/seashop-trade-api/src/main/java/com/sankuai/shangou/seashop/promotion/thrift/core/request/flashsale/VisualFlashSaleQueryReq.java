package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/16/016
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "限时购可视化查询请求对象")
public class VisualFlashSaleQueryReq extends BasePageReq {

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "类目路径 | 隔开")
    private String categoryPath;

    @Schema(description = "店铺分类id")
    private Long shopCategoryId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "类目id列表")
    private List<Long> categoryIds;

    @Schema(description = "类目id列表")
    private List<Long> productIds;

    @Schema(description = "限时购id列表")
    private List<Long> flashSaleIds;


}
