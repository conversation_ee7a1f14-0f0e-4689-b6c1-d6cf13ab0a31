package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CheckBrandNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandApplyDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.CheckBrandNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandApplyDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 * @Description: 品牌申请记录查询服务
 */
@FeignClient(name = "himall-trade", contextId = "BrandApplyQueryFeign", path = "/himall-trade/brandApply", url = "${himall-trade.dev.url:}")
public interface BrandApplyQueryFeign {

    /**
     * 查询品牌申请列表
     */
    @PostMapping(value = "/queryBrandApplyForPage", consumes = "application/json")
    ResultDto<BasePageResp<BrandApplyDto>> queryBrandApplyForPage(@RequestBody QueryBrandApplyReq request) throws TException;

    /**
     * 查询品牌申请记录详情(供应商)
     */
    @PostMapping(value = "/queryBrandApplyDetailForSeller", consumes = "application/json")
    ResultDto<BrandApplyDetailResp> queryBrandApplyDetailForSeller(@RequestBody QueryBrandApplyDetailReq request) throws TException;

    /**
     * 查询品牌申请记录详情(平台端)
     */
    @PostMapping(value = "/queryBrandApplyDetailForPlatForm", consumes = "application/json")
    ResultDto<BrandApplyDetailResp> queryBrandApplyDetailForPlatForm(@RequestBody QueryBrandApplyDetailReq request) throws TException;

    /**
     * 校验品牌名称
     */
    @PostMapping(value = "/checkBrandName", consumes = "application/json")
    ResultDto<CheckBrandNameResp> checkBrandName(@RequestBody CheckBrandNameReq request) throws TException;

}
