package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/04/29 9:19
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询组合购买/限时购商品集合")
public class CollocationFlashSaleProductReq extends BaseParamReq {

    @Schema(description = "店铺id", required = true)
    private Long shopId;

    @Schema(description = "是否包括附属商品(默认包括)")
    private Boolean includeSubProduct;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(shopId, "店铺id不能为空");
    }


}
