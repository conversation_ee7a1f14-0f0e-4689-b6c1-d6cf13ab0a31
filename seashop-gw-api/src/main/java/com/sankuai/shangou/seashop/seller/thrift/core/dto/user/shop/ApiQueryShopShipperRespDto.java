package com.sankuai.shangou.seashop.seller.thrift.core.dto.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description：查询供应商发/退货地址表返参
 * @author： liweisong
 * @create： 2023/11/27 9:59
 */
@Data
@ToString
@TypeDoc(description = "查询供应商发/退货地址表返参")
public class ApiQueryShopShipperRespDto extends BaseParamReq {

    @FieldDoc(description = "ID")
    private Long id;

    @FieldDoc(description = "商家编号")
    private Long shopId;

    @FieldDoc(description = "是否为默认发货地址")
    private Boolean defaultSendGoodsFlag;

    @FieldDoc(description = "是否默认收货地址")
    private Boolean defaultGetGoodsFlag;

    @FieldDoc(description = "发货点名称")
    private String shipperTag;

    @FieldDoc(description = "发货人")
    private String shipperName;

    @FieldDoc(description = "区域ID")
    private Integer regionId;

    @FieldDoc(description = "具体街道信息")
    private String address;

    @FieldDoc(description = "手机号码")
    private String telPhone;

    @FieldDoc(description = "微信OpenID用于发信息到微信给发货人")
    private String wxOpenId;

    @FieldDoc(description = "经度")
    private BigDecimal longitude;

    @FieldDoc(description = "纬度")
    private BigDecimal latitude;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;

    @FieldDoc(description = "省份名称")
    private String provinceName;

    @FieldDoc(description = "城市名称")
    private String cityName;

    @FieldDoc(description = "区域名称")
    private String countyName;

    @FieldDoc(description = "乡镇名称")
    private String townsNames;


    public String getLongitudeString() {
        return this.bigDecimal2String(this.longitude);
    }


    public void setLongitudeString(String longitude) {
        this.longitude = this.string2BigDecimal(longitude);
    }


    public String getLatitudeString() {
        return this.bigDecimal2String(this.latitude);
    }


    public void setLatitudeString(String latitude) {
        this.latitude = this.string2BigDecimal(latitude);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
