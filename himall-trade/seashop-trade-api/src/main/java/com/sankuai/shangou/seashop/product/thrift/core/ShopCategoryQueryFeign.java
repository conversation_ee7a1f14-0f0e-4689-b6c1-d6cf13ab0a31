package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.QueryShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryIdsResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.shopcategory.ShopCategoryTreeResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 店铺分类查询服务
 *
         * author HuBiao
 * date 2023/11/13 11:05
        */
@FeignClient(name = "himall-trade", contextId = "ShopCategoryQueryFeign", path = "/himall-trade/shopCategory", url = "${himall-trade.dev.url:}")
public interface ShopCategoryQueryFeign {

    /**
     * 查询店铺分类
     */
    @PostMapping(value = "/queryShopCategory", consumes = "application/json")
    ResultDto<ShopCategoryTreeResp> queryShopCategory(@RequestBody QueryShopCategoryReq request) throws TException;

    /**
     * 查询下级类目id的集合
     */
    @PostMapping(value = "/getSubShopCategoryIds", consumes = "application/json")
    ResultDto<ShopCategoryIdsResp> getSubShopCategoryIds(@RequestBody BaseIdReq request) throws TException;

}
