package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.Arrays;
import java.util.List;

import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象商品保存处理器
 *
 * <AUTHOR>
 * @date 2023/11/22 19:19
 */
@Slf4j
public abstract class AbsSaveProductHandler extends AbsProductHandler {

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】{}开始, context:{}", getHandlerName(), context);
        boolean editFlag = context.isEditFlag();
        // 创建商品
        if (!editFlag) {
            log.info("【保存商品】{}创建: productId： {}", getHandlerName(), context.getProductId());
            create(context);
            log.info("【保存商品】{}结束, context:{}", getHandlerName(), context);
            return;
        }

        // 更新商品前置逻辑
        beforeUpdate(context);

        // 保存到草稿
        if (context.isDraftFlag()) {
            log.info("【保存商品】{}保存到草稿: productId： {}", getHandlerName(), context.getProductId());
            updateDraft(context);
            log.info("【保存商品】{}结束, context:{}", getHandlerName(), context);
            return;
        }

        // 编辑
        log.info("【保存商品】{}直接保存: productId： {}", getHandlerName(), context.getProductId());
        update(context);
        log.info("【保存商品】{}结束, context:{}", getHandlerName(), context);

        // 更新商品后置逻辑
        postUpdate(context);
    }

    /**
     * 创建逻辑
     *
     * @param context 保存商品上下文对象
     */
    public abstract void create(ProductContext context);

    /**
     * 更新商品前置逻辑
     *
     * @param context 保存商品上下文对象
     */
    public void beforeUpdate(ProductContext context) {
    }

    /**
     * 更新商品逻辑
     *
     * @param context 保存商品上下文对象
     */
    public abstract void update(ProductContext context);

    /**
     * 保存到草稿
     *
     * @param context 保存商品上下文对象
     */
    public abstract void updateDraft(ProductContext context);

    /**
     * 更新商品后置逻辑
     *
     * @param context 保存商品上下文对象
     */
    public void postUpdate(ProductContext context) {
    }

    public abstract String getHandlerName();
}
