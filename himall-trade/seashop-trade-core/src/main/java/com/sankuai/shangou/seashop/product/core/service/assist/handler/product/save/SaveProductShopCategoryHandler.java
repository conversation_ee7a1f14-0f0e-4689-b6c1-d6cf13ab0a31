package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductShopCategoryRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 保存商品阶梯价信息
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
@Component
@Slf4j
public class SaveProductShopCategoryHandler extends AbsSaveProductHandler {

    @Resource
    private ProductShopCategoryRepository productShopCategoryRepository;

    /**
     * 创建店铺分类
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void create(ProductContext context) {
        ProductBo productBo = context.getSaveProductBo();
        Long productId = context.getProductId();
        List<Long> shopCategoryIdList = productBo.getShopCategoryIdList();
        List<ProductShopCategory> relateList = shopCategoryIdList.stream().map(shopCategoryId -> {
            ProductShopCategory productShopCategory = new ProductShopCategory();
            productShopCategory.setProductId(productId);
            productShopCategory.setShopCategoryId(shopCategoryId);
            return productShopCategory;
        }).collect(Collectors.toList());
        productShopCategoryRepository.saveBatch(relateList);
    }

    /**
     * 更新店铺分类
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void update(ProductContext context) {
        productShopCategoryRepository.remove(new LambdaQueryWrapper<ProductShopCategory>().eq(ProductShopCategory::getProductId, context.getProductId()));
        create(context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getShopCategoryIdList() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT_SHOP_CATEGORY;
    }

    /**
     * 更新草稿店铺分类
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void updateDraft(ProductContext context) {
        update(context);
    }

    @Override
    public String getHandlerName() {
        return "关联店铺分类";
    }

}
