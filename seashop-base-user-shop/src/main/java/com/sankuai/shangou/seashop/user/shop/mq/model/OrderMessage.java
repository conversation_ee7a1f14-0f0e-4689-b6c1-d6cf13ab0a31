package com.sankuai.shangou.seashop.user.shop.mq.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.shangou.seashop.user.shop.mq.event.OrderMessageEvent;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class OrderMessage implements Serializable {

    @JsonProperty("orderId")
    private String orderId;
    /**
     * 订单事件。{@link  OrderMessageEvent}
     */
    @JsonProperty("orderEventName")
    private String orderEventName;

}
