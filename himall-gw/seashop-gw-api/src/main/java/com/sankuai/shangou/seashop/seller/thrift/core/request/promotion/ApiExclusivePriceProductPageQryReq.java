package com.sankuai.shangou.seashop.seller.thrift.core.request.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "专享价活动商品查询请求对象")
public class ApiExclusivePriceProductPageQryReq extends BasePageReq {

    @FieldDoc(description = "活动ID", requiredness = Requiredness.REQUIRED)
    private Long activeId;


}
