package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单明细退款预览信息对象")
public class OrderItemRefundPreviewResp extends BaseThriftDto {

    @FieldDoc(description = "订单ID")
    private String orderId;
    @FieldDoc(description = "商品ID")
    private String productId;
    @FieldDoc(description = "商品SKU ID")
    private String skuId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "商品主图")
    private String mainImagePath;
    @FieldDoc(description = "购买数量")
    private Long quantity;
    @FieldDoc(description = "商品总价")
    private BigDecimal productAmount;
    @FieldDoc(description = "联系人姓名")
    private String contactUserName;
    @FieldDoc(description = "联系人电话")
    private String contactUserPhone;
    @FieldDoc(description = "可退金额描述(最多X元，包含运费Y元)")
    private BigDecimal remainRefundAmountDesc;
    @FieldDoc(description = "退款ID")
    private Long refundId;

    /**
     * 售后类型。1：订单退款
     */
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款")
    private Integer refundType;
    /**
     * 售后类型描述
     */
    @FieldDoc(description = "售后类型描述")
    private String refundTypeDesc;
    /**
     * 退款模式。1：订单退款；2：货品退款；3：退货退款
     */
    @FieldDoc(description = "退款模式。1：订单退款；2：货品退款；3：退货退款")
    private Integer refundMode;
    /**
     * 退款模式描述
     */
    @FieldDoc(description = "退款模式描述")
    private String refundModeDesc;
    /**
     * 申请退款金额
     */
    @FieldDoc(description = "申请退款金额")
    private BigDecimal refundAmount;
    /**
     * 退款数量。退货退款时有值
     */
    @FieldDoc(description = "退款数量。退货退款时有值")
    private Long refundQuantity;
    /**
     * 退款原因类型
     */
    @FieldDoc(description = "退款原因类型")
    private String refundReasonDesc;
    /**
     * 退款说明
     */
    @FieldDoc(description = "退款说明")
    private String refundRemark;
    /**
     * 退款方式。1：原路返回
     */
    @FieldDoc(description = "退款方式。1：原路返回")
    private Integer refundPayType;
    /**
     * 退款方式描述
     */
    @FieldDoc(description = "退款方式描述")
    private String refundPayTypeDesc;
    /**
     * 售后凭证1。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证1。最多三张，与数据表保持一致分开")
    private String certPic1;
    /**
     * 售后凭证2。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证2。最多三张，与数据表保持一致分开")
    private String certPic2;
    /**
     * 售后凭证3。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证3。最多三张，与数据表保持一致分开")
    private String certPic3;
    /**
     * 商品计量单位
     */
    @FieldDoc(description = "商品计量单位")
    private String measureUnit;
    /**
     * 订单状态
     */
    @FieldDoc(description = "订单状态")
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    @FieldDoc(description = "订单状态描述")
    private String orderStatusDesc;
    @FieldDoc(description = "明细可退款金额")
    private BigDecimal remainRefundAmount;
    /**
     * 明细可退款数量
     */
    @FieldDoc(description = "明细可退款数量")
    private Long remainRefundQuantity;
    /**
     * 计算营销均摊后的商品单价，realTotalPrice/quantity
     */
    @FieldDoc(description = "计算营销均摊后的商品单价，realTotalPrice/quantity ")
    private BigDecimal realSalePrice;


    public String getProductAmountString() {
        return this.bigDecimal2String(this.productAmount);
    }


    public void setProductAmountString(String productAmount) {
        this.productAmount = this.string2BigDecimal(productAmount);
    }


    public String getRemainRefundAmountDescString() {
        return this.bigDecimal2String(this.remainRefundAmountDesc);
    }


    public void setRemainRefundAmountDescString(String remainRefundAmountDesc) {
        this.remainRefundAmountDesc = this.string2BigDecimal(remainRefundAmountDesc);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public String getRemainRefundAmountString() {
        return this.bigDecimal2String(this.remainRefundAmount);
    }


    public void setRemainRefundAmountString(String remainRefundAmount) {
        this.remainRefundAmount = this.string2BigDecimal(remainRefundAmount);
    }


    public String getRealSalePriceString() {
        return this.bigDecimal2String(this.realSalePrice);
    }


    public void setRealSalePriceString(String realSalePrice) {
        this.realSalePrice = this.string2BigDecimal(realSalePrice);
    }
}
