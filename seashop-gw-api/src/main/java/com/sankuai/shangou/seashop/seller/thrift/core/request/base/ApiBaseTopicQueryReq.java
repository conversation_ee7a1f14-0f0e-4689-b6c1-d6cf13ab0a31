package com.sankuai.shangou.seashop.seller.thrift.core.request.base;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

@TypeDoc(description = "专题查询对象")
@Data
public class ApiBaseTopicQueryReq extends BasePageReq {

    @FieldDoc(description = "专题名称")
    private String name;

    @FieldDoc(description = "专题标签")
    private String tags;


    @FieldDoc(description = "shopId")
    private Long shopId;

    @FieldDoc(description = "shopId")
    private Integer platForm;


}
