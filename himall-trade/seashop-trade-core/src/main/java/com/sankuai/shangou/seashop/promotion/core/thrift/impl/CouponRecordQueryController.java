package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponRecordQueryBo;
import com.sankuai.shangou.seashop.promotion.core.service.CouponRecordService;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordSimpleDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordIdOrSnReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderListResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponRecordQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
@RestController
@RequestMapping("/couponRecord")
public class CouponRecordQueryController implements CouponRecordQueryFeign {

    @Resource
    private CouponRecordService couponRecordService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<CouponRecordSimpleResp>> pageList(@RequestBody CouponRecordQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            CouponRecordQueryBo queryBo = JsonUtil.copy(req, CouponRecordQueryBo.class);
            BasePageResp<CouponRecordSimpleDto> couponRecordListDtoBasePageResp = couponRecordService.pageList(queryBo);
            return PageResultHelper.transfer(couponRecordListDtoBasePageResp, CouponRecordSimpleResp.class);
        });
    }

    @PostMapping(value = "/getByIdOrSn", consumes = "application/json")
    @Override
    public ResultDto<CouponRecordSimpleListResp> getByIdOrSn(@RequestBody CouponRecordIdOrSnReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getByIdOrSn", request, req -> {
            req.checkParameter();
            return couponRecordService.getByIdOrSn(req);
        });
    }

    @PostMapping(value = "/getUserRecordById", consumes = "application/json")
    @Override
    public ResultDto<CouponRecordSimpleResp> getUserRecordById(@RequestBody CouponRecordIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getById", request, req -> {
            req.checkParameter();
            return couponRecordService.getUserRecordById(req);
        });
    }

    @PostMapping(value = "/getRecordByOrder", consumes = "application/json")
    @Override
    public ResultDto<CouponRecordOrderListResp> getRecordByOrder(@RequestBody PromotionRecordOrderQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getRecordByOrder", request, req -> {
            req.checkParameter();
            return couponRecordService.getRecordByOrder(req);
        });
    }
}
