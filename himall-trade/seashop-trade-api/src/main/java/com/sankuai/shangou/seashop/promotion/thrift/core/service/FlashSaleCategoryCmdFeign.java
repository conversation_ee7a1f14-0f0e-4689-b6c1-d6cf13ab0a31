package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCategoryAddReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description: 限时购分类操作服务
 */
@FeignClient(name = "himall-trade",contextId = "FlashSaleCategoryCmdFeign", path = "/himall-trade/flashSaleCategory", url = "${himall-trade.dev.url:}")
public interface FlashSaleCategoryCmdFeign {

    /**
     * 新增限时购分类
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/add", consumes = "application/json")
    ResultDto<BaseResp> add(@RequestBody FlashSaleCategoryAddReq request) throws TException;

    /**
     * 删除限时购分类
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<BaseResp> delete(@RequestBody BaseIdReq request) throws TException;
}
