package com.sankuai.shangou.seashop.m.thrift.order.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家查询售后记录请求参数")
public class ApiPlatformQueryRefundReq extends BasePageReq {

    @FieldDoc(description = "平台用户信息", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "订单号")
    private String orderId;
    @FieldDoc(description = "买家账号。等价于EP账号")
    private String userName;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "申请开始时间")
    private Date applyTimeStart;
    @FieldDoc(description = "申请结束时间")
    private Date applyTimeEnd;
    @FieldDoc(description = "商品ID/规格ID")
    private String productIdOrSkuId;
    @FieldDoc(description = "退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；6：待平台确认；7：退款成功；8：平台驳回；9：退款中；-1：买家取消售后申请")
    private Integer refundStatus;
    @FieldDoc(description = "查询类型。1：退款-全部；2：退款-待处理；3：退货-全部；4：退货-待处理", requiredness = Requiredness.REQUIRED)
    private Integer tab;


    @Override
    public void checkParameter() {
        if (tab == null) {
            throw new InvalidParamException("查询类型不能为空");
        }
    }


    public Long getApplyTimeStartLong() {
        return this.date2Long(this.applyTimeStart);
    }


    public void setApplyTimeStartLong(Long applyTimeStart) {
        this.applyTimeStart = this.long2Date(applyTimeStart);
    }


    public Long getApplyTimeEndLong() {
        return this.date2Long(this.applyTimeEnd);
    }


    public void setApplyTimeEndLong(Long applyTimeEnd) {
        this.applyTimeEnd = this.long2Date(applyTimeEnd);
    }


}
