package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class DiscountActiveSaveBo extends BaseParamReq {

    //主键
    @PrimaryField
    private Long id;

    //店铺ID
    private Long shopId;

    //活动名称
    @ExaminField(description = "活动名称")
    private String activeName;

    //开始时间
    @ExaminField(description = "开始时间")
    private Date startTime;

    //结束时间
    @ExaminField(description = "结束时间")
    private Date endTime;

//    @ExaminField(isChildField = true,entityClassName = "com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveRule")
    private List<DiscountActiveRuleBo> ruleList;

    private List<Long> productIdList;
}
