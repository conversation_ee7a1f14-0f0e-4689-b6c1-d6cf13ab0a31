package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.Optional;

import com.sankuai.shangou.seashop.product.core.service.model.DescriptionTemplateBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionTemplate;
import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:53
 */
public class DescriptionTemplateConverter {

    public static ProductDescriptionTemplate convertToEntity(DescriptionTemplateBo descriptionTemplateBo) {
        return Optional.ofNullable(descriptionTemplateBo).map(bo -> {
            ProductDescriptionTemplate entity = new ProductDescriptionTemplate();
            entity.setId(bo.getId());
            entity.setName(bo.getName());
            if (bo.getPosition() != null) {
                entity.setPosition(bo.getPosition().getCode());
            }
            entity.setContent(bo.getContent());
            entity.setMobileContent(bo.getMobileContent());
            entity.setShopId(bo.getShopId());
            return entity;
        }).orElse(null);
    }

    public static DescriptionTemplateBo convertToBo(ProductDescriptionTemplate descriptionTemplate) {
        return Optional.ofNullable(descriptionTemplate).map(entity -> {
            DescriptionTemplateBo bo = new DescriptionTemplateBo();
            bo.setId(entity.getId());
            bo.setName(entity.getName());
            DescriptionTemplatePositionEnum position = DescriptionTemplatePositionEnum.getByCode(descriptionTemplate.getPosition());
            if (position != null) {
                bo.setPosition(position);
                bo.setPositionCode(position.getCode());
                bo.setPositionDesc(position.getDesc());
            }
            bo.setContent(entity.getContent());
            bo.setMobileContent(entity.getMobileContent());
            bo.setShopId(entity.getShopId());
            return bo;
        }).orElse(null);
    }

}
