package com.sankuai.shangou.seashop.product.core.service;

import com.sankuai.shangou.seashop.product.core.service.model.RollBackStockBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;

/**
 * <AUTHOR>
 * @date 2023/12/18 15:49
 */
public interface SkuStockService {

    /**
     * 同步修改库存
     *
     * @param taskBo 库存入参
     */
    void syncChangeStock(StockTaskBo taskBo);

    /**
     * 异步修改库存
     *
     * @param taskBo 库存入参
     */
    void asyncChangeSkuStock(StockTaskBo taskBo);

    /**
     * 执行具体异步覆盖库存的逻辑
     *
     * @param taskInfoId 同步库存明细id
     */
    void executeAsyncStock(Long taskInfoId);

    /**
     * 回滚库存
     *
     * @param rollBackBo 回滚库存入参
     */
    void rollBackSkuStock(RollBackStockBo rollBackBo);
}
