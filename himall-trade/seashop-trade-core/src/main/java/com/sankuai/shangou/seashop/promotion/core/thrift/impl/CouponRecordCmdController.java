package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.promotion.core.service.CouponRecordService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordCancelConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponRecordCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/15/015
 * @description:
 */
@RestController
@RequestMapping("/couponRecord")
public class CouponRecordCmdController implements CouponRecordCmdFeign {

    @Resource
    private CouponRecordService couponRecordService;

    @PostMapping(value = "/consume", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> consume(@RequestBody CouponRecordConsumeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("consume", request, req -> {
            req.checkParameter();

            couponRecordService.consume(req);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/cancelConsume", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> cancelConsume(@RequestBody CouponRecordCancelConsumeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("cancelConsume", request, req -> {
            req.checkParameter();

            couponRecordService.cancelConsume(req);
            return BaseResp.of();
        });
    }
}
