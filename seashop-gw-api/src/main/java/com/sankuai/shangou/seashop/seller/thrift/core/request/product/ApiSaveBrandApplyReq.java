package com.sankuai.shangou.seashop.seller.thrift.core.request.product;

import com.hishop.starter.storage.annotation.jackson.JsonUrlFormat;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/07 14:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "申请品牌入参")
public class ApiSaveBrandApplyReq extends BaseParamReq {

    @FieldDoc(description = "申请记录id 新增时不填", requiredness = Requiredness.NONE)
    private Long id;

    @FieldDoc(description = "申请类型 1-平台已有 2-新品牌", requiredness = Requiredness.REQUIRED)
    private Integer applyModeCode;

    @FieldDoc(description = "申请品牌 applyMode=1 必填", requiredness = Requiredness.NONE)
    private Long brandId;

    @FieldDoc(description = "品牌名称 applyMode=2 必填", requiredness = Requiredness.NONE)
    private String brandName;

    @FieldDoc(description = "logo applyMode=2 必填", requiredness = Requiredness.NONE)
    @JsonUrlFormat(deserializer = false)
    private String logo;

    @FieldDoc(description = "描述 applyMode=2 必填", requiredness = Requiredness.NONE)
    private String description;

    @FieldDoc(description = "授权证书", requiredness = Requiredness.REQUIRED)
    @JsonUrlFormat(deserializer = false)
    private List<String> authCertificateList;

    @FieldDoc(description = "备注", requiredness = Requiredness.NONE)
    private String remark;


}
