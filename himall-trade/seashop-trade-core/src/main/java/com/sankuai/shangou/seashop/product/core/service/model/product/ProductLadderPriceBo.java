package com.sankuai.shangou.seashop.product.core.service.model.product;

import java.math.BigDecimal;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/13 19:19
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductLadderPriceBo {

    /**
     * 阶梯价Id
     */
    private Long ladderPriceId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 阶梯价起购量
     */
    @ExaminField(description = "阶梯价起购量")
    private Integer minBath;

    /**
     * 阶梯价最大购买量
     */
    private Integer maxBath;

    /**
     * 阶梯价单价
     */
    @ExaminField(description = "阶梯价单价")
    private BigDecimal price;
}
