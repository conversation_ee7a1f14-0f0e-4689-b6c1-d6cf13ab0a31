package com.sankuai.shangou.seashop.user.shop.service.impl;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.PageUtil;
import com.sankuai.shangou.seashop.product.thrift.core.BrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.BatchQueryBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.BrandListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.BrandDto;
import com.sankuai.shangou.seashop.user.common.constant.EsConstant;
import com.sankuai.shangou.seashop.user.common.es.EagleShopService;
import com.sankuai.shangou.seashop.user.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.user.common.es.model.shop.EsShopParam;
import com.sankuai.shangou.seashop.user.common.es.service.EsShopService;
import com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.user.shop.service.ShopEsService;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryUserInvisibleShopReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.BusinessCategoryEsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopBrandEsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsCombinationResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsResp;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@Service
@Slf4j
public class ShopEsServiceImpl implements ShopEsService {

    @Resource
    private EsShopService esShopService;
    @Resource
    protected EagleShopService eagleShopService;
    @Resource
    private RemoteCategoryService remoteCategoryService;
    @Resource
    private BrandQueryFeign brandQueryFeign;

    @Override
    public ShopEsCombinationResp searchShopEs(ShopEsQueryReq shopEsQueryReq) {
        // 转换成最下级类目id 去查询
        /*if (shopEsQueryReq.getCategoryId() != null) {
            shopEsQueryReq.setCategoryIdList(remoteCategoryService.getLastCategoryIds(shopEsQueryReq.getCategoryId()));
            shopEsQueryReq.setCategoryId(null);
        }*/

        // 构建搜索请求
        SearchRequest searchRequest = buildSearchRequest(shopEsQueryReq);

        log.info("【店铺搜索】搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleShopService.queryByCondition(searchRequest);
        log.info("【店铺搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(shopEsQueryReq), searchResult);
        // 解析搜索结果
        ShopEsCombinationResp resultBo = resolveSearchResult(searchResult, shopEsQueryReq);
        log.debug("【店铺搜索】搜索条件为: {}, 搜索结果为: {}", JsonUtil.toJsonString(shopEsQueryReq), JsonUtil.toJsonString(resultBo));

        return resultBo;
    }

    /**
     * 获取用户不可见的店铺
     * <p>所谓不可见店铺，是指店铺开启了专属商家，且用户不属于店铺的专享商家</p>
     * <AUTHOR>
     * @param queryReq
     * java.util.List<java.lang.Long>
     */
    public List<Long> getUserInvisibleShop(QueryUserInvisibleShopReq queryReq) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 如果用户未登录，直接返回所有开启了专属商家的店铺，否则返回开启了专属商家且当前用户不是专属商家
        boolQueryBuilder.must(QueryBuilders.termQuery("whetherOpenExclusiveMember", true));
        if (queryReq.getUserId() != null) {
            // 当前用户不是店铺的专属商家
            boolQueryBuilder.mustNot(QueryBuilders.termQuery(EsConstant.Shop.SEARCH_FIELD_EXCLUSIVE_MEMBER_IDS, queryReq.getUserId()));
        }
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .fetchSource(new String[]{EsConstant.Shop.SEARCH_FIELD_SHOP_ID}, null)
                .from(0)
                .size(EsConstant.Shop.DEFAULT_SEARCH_SIZE);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(EsConstant.INDEX_SHOP);
        searchRequest.source(sourceBuilder);
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleShopService.queryByCondition(searchRequest);
        if (searchResult.getTotalHit() == null || searchResult.getTotalHit() == 0) {
            return null;
        }
        return searchResult.getHits().stream()
                .map(item -> JsonUtil.parseObject(item, ShopEsResp.class))
                .map(ShopEsResp::getShopId)
                .collect(Collectors.toList());
    }



    private SearchRequest buildSearchRequest(ShopEsQueryReq request) {

        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = esShopService.buildProductSearchCondition(JsonUtil.copy(request, EsShopParam.class));
        // 构建聚合查询
        // 包括：品牌的brandName和logo，分类的categoryName
        List<AggregationBuilder> aggregationBuilders = buildAggregationList(request.getSearchLastCategory());
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildProductFieldSortList(request.getSortList());
        // 整合查询条件
        int from = PageUtil.getStart(request.getPageNo(), request.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(request.getPageSize());
        // 设置聚合
        aggregationBuilders.forEach(sourceBuilder::aggregation);
        // 设置排序
        sortBuilders.forEach(sourceBuilder::sort);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(EsConstant.INDEX_SHOP);
        searchRequest.source(sourceBuilder);
        return searchRequest;

    }

    private List<AggregationBuilder> buildAggregationList(Boolean searchLastCategory) {
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();

        // 构建聚合查询

        AggregationBuilder brandsAgg = AggregationBuilders
                .terms(EsConstant.Shop.AGG_FIELD_BRANDS)
                .field(EsConstant.Shop.AGG_FIELD_LIST_BRAND_ID)
                .size(20000);

        AggregationBuilder categorysAgg = AggregationBuilders
                .terms(EsConstant.Shop.AGG_FIELD_CATEGORYS)
                .field(searchLastCategory != null && searchLastCategory
                        ? EsConstant.Shop.AGG_FIELD_LIST_CATEGORY_ID : EsConstant.Shop.AGG_FIELD_FIRST_CATEGORY_ID)
                .size(20000);

        aggregationBuilders.add(brandsAgg);
        aggregationBuilders.add(categorysAgg);

        return aggregationBuilders;
    }

    private List<SortBuilder<FieldSortBuilder>> buildProductFieldSortList(List<FieldSortReq> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            sortList = defaultSortList();
        }
        if (CollectionUtils.isEmpty(sortList)) {
            return Lists.newArrayList();
        }

        return sortList.stream()
                .map(sf -> SortBuilders.fieldSort(sf.getSort()).order(sf.getIzAsc() ? SortOrder.ASC : SortOrder.DESC))
                .collect(Collectors.toList());
    }

    private ShopEsCombinationResp resolveSearchResult(EagleQueryResult searchResult, ShopEsQueryReq shopEsQueryReq) {
        log.info("【店铺搜索】搜索结果为: {}", JsonUtil.toJsonString(searchResult));
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            ShopEsCombinationResp shopEsCombinationResp = ShopEsCombinationResp.defaultEmpty();
            shopEsCombinationResp.setShopList(PageResultHelper.defaultEmpty(shopEsQueryReq));
            return shopEsCombinationResp;
        }

        List<ShopEsResp> shopEsList = searchResult.getHits().stream().map(hit -> JsonUtil.parseObject(hit, ShopEsResp.class)).collect(Collectors.toList());

        Terms brands = searchResult.getAggregations().get(EsConstant.Shop.AGG_FIELD_BRANDS);
        List<Long> brandsIds = getDirectKeyLong(brands);
        List<BrandDto> brandList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(brandsIds)) {
            brandsIds = brandsIds.stream().distinct().collect(Collectors.toList());
            BatchQueryBrandReq req = new BatchQueryBrandReq();
            req.setIdList(brandsIds);
            req.setUseCache(true);
            BrandListResp resp = ThriftResponseHelper.executeThriftCall(() -> brandQueryFeign.queryBrandList(req));
            brandList = resp.getBrandList();
        }

        List<ShopBrandEsResp> shopBrandList = JsonUtil.copyList(brandList, ShopBrandEsResp.class, (source, target) -> {
            target.setBrandId(source.getId());
            target.setBrandName(source.getName());
        });


        Terms categoryAgg = searchResult.getAggregations().get(EsConstant.Shop.AGG_FIELD_CATEGORYS);
        List<Long> categoryIds = getDirectKeyLong(categoryAgg);
        // 需要排序
        List<RemoteCategoryBo> categoryList = remoteCategoryService.queryCacheCategoryList(categoryIds)
                .stream()
                .filter(remoteCategoryBo -> remoteCategoryBo.getWhetherShow() != null && remoteCategoryBo.getWhetherShow())
                .filter(remoteCategoryBo -> remoteCategoryBo.getWhetherDelete() == null || !remoteCategoryBo.getWhetherDelete())
                .sorted(Comparator.comparing(RemoteCategoryBo::getDisplaySequence))
                .collect(Collectors.toList());

        List<BusinessCategoryEsResp> businessCategoryList = JsonUtil.copyList(categoryList, BusinessCategoryEsResp.class, (source, target) -> {
            target.setCategoryId(source.getId());
            target.setCategoryName(source.getName());
            target.setFullCategoryName(source.getFullCategoryName());
            target.setWhetherFrozen(false);
        });

        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<ShopEsResp> shopPage = new BasePageResp<>();
        shopPage.setData(shopEsList);
        shopPage.setPages(PageUtil.totalPage(totalHit, shopEsQueryReq.getPageSize()));
        shopPage.setTotalCount(searchResult.getTotalHit());
        shopPage.setPageNo(shopEsQueryReq.getPageNo());
        shopPage.setPageSize(shopEsQueryReq.getPageSize());

        return ShopEsCombinationResp.builder()
                .shopList(shopPage)
                .shopBrandList(shopBrandList)
                .businessCategoryList(businessCategoryList)
                .shopBrandIds(brandsIds)
                .categoryIds(categoryIds).build();
    }

    private List<Long> getDirectKeyLong(Terms aggTerms) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(aggTerms.getBuckets())) {
            return Collections.emptyList();
        }
        List<Long> idList = new ArrayList<>(aggTerms.getBuckets().size());
        // 遍历品牌聚合桶
        for (Terms.Bucket bucket : aggTerms.getBuckets()) {
            String id = bucket.getKeyAsString();
            idList.add(Long.valueOf(id));
        }
        return idList;
    }

    private List<FieldSortReq> defaultSortList() {
        return null;
    }
}
