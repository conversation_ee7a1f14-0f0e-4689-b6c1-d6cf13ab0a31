package com.sankuai.shangou.seashop.promotion.thrift.core.constant;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
public class PromotionApiConstant {

    /**
     * 全部商品
     */
    public static final Long ALL_PRODUCT = -1L;

    /**
     * 优惠券最大领取数量
     */
    public static final Integer COUPON_MAX_RECEIVE_NUM = 5;

    /**
     * 折扣活动最大层级数量
     */
    public static final Integer DISCOUNT_ACTIVE_MAX_LEVEL = 5;

    /**
     * 优惠券领取key
     */
    public static final String COUPON_RECEIVE_KEY = "promotion:coupon:receive:{0}:{1}";

    /**
     * 限时购活动默认标题
     */
    public static final String LIMIT_TIME_ACTIVE_TITLE = "限时活动";

    /**
     * in查询的最大数量200
     */
    public static final Integer IN_MAX_NUM = 200;

    /**
     * 正则表达式，匹配中文字符、数字、英文字母和下划线
     */
    public static String NORMAL_CHARACTER_REGEX = "^[\u4e00-\u9fff0-9a-zA-Z_]+$";
}
