package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.pass;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/24 16:13
 */
@Component
@Slf4j
public class ProductAuditPassHandler extends AbsProductAuditPassHandler {

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductAuditRepository productAuditRepository;

    private static final String[] IGNORE_FIELDS = new String[]{"saleCounts", "addedDate", "createTime"};

    @Override
    protected void handle(ProductContext context) {
        log.info("【商品审核通过】保存商品数据==开始, context={}", context);

        Long productId = context.getProductId();
        ProductAudit productAudit = productAuditRepository.getByProductId(productId);
        AssertUtil.throwIfNull(productAudit, "未找到审核记录");
        Product product = JsonUtil.copy(productAudit, Product.class, IGNORE_FIELDS);
        product.setWhetherNewProduct(Boolean.FALSE);
        product.setCheckTime(new Date());
        productRepository.updateByProductId(product);
        context.setShopId(product.getShopId());

        log.info("【商品审核通过】保存商品数据==结束, context={}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.PASS_PRODUCT_AUDIT;
    }

}
