package com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "专享价商品响应对象")
public class ApiExclusivePriceProductDto extends BaseThriftDto {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "活动ID")
    private Long activeId;

    @FieldDoc(description = "商品ID")
    private String productId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "skuId")
    private String skuId;

    @FieldDoc(description = "sku自增id")
    private Long skuAutoId;

    @FieldDoc(description = "sku名称")
    private String skuName;

    @FieldDoc(description = "商城价格")
    private BigDecimal mallPrice;

    @FieldDoc(description = "专享价格")
    private BigDecimal price;

    @FieldDoc(description = "会员id")
    private Long memberId;

    @FieldDoc(description = "会员名称")
    private String userName;


    public String getMallPriceString() {
        return this.bigDecimal2String(this.mallPrice);
    }


    public void setMallPriceString(String mallPrice) {
        this.mallPrice = this.string2BigDecimal(mallPrice);
    }


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }


}
