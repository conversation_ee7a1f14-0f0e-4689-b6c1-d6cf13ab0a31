package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionAuditRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 查询商品审核处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryProductDetailAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private ProductDescriptionAuditRepository productDescriptionAuditRepository;
    @Resource
    private ProductAssist productAssist;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        // 查询商品基本信息
        ProductBo productBo = context.getOldProductBo();

        // 查询详情
        ProductDescriptionAudit description  = productDescriptionAuditRepository.getProductDescriptionByProductId(productId);
        AssertUtil.throwIfNull(description, "商品详情不存在");
        productBo.setDescriptionPrefixId(productAssist.getValidDescriptionTemplateId(description.getDescriptionPrefixId()));
        productBo.setDescriptionSuffixId(productAssist.getValidDescriptionTemplateId(description.getDescriptionSuffixId()));
        productBo.setDescription(description.getDescription());
        productBo.setMobileDescription(description.getMobileDescription());
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_PRODUCT_DETAIL_AUDIT;
    }

}
