package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.query;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品查询处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryProductHandler extends AbsProductHandler {

    @Resource
    private ProductAssist productAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_DETAIL);
    }

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        // 查询商品基本信息
        Product product = productAssist.checkProductAuth(productId, context.getShopId(), Boolean.TRUE);
        ProductBo productBo = JsonUtil.copy(product, ProductBo.class);
        ProductStatusEnum statusEnum = ProductStatusEnum.getBySaleAuditStatus(product.getSaleStatus(), product.getAuditStatus());
        productBo.setStatus(statusEnum.getCode());
        productBo.setStatusDesc(statusEnum.getDesc());
        productBo.setSourceDesc(ProductSourceEnum.getDescByCode(product.getSource()));
        context.setOldProductBo(productBo);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_PRODUCT;
    }

}
