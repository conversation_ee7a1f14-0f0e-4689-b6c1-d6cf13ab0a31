package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.AdvanceSaveReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:提供弹窗广告操作功能
 */
@FeignClient(name = "himall-trade",contextId = "AdvanceCmdFeign", path = "/himall-trade/advance", url = "${himall-trade.dev.url:}")
public interface AdvanceCmdFeign {

    /**
     * 保存弹窗广告（包含新增/修改）
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody AdvanceSaveReq request) throws TException;
}
