package com.sankuai.shangou.seashop.user.shop.mq.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: lhx
 * @date: 2023/3/8/008
 * @description: 汇付审核返回对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdaAuditDTO implements Serializable {

    /**
     *  "member_id": "*************",
     *     "created_time": "**********",
     *     "object":"corp_member",
     *     "order_no": "*********",
     *     "prod_mode": "true",
     *     "app_id": "sfjeijibbTe5jLGCi5rzfH4OqPW9KCif913",
     *     "audit_state": "D",
     *     "audit_desc": "开户成功",
     *     "settle_account_id": "****************"
     */
    /**
     * 汇付团员id
     */
    private String memberId;

    /**
     * 审核状态:
     * D为审核成功
     */
    private String auditState;

    /**
     * 失败原因
     */
    private String auditDesc;

    /**
     * 结算账号
     */
    private String settleAccountId;

    /**
     * 操作类型
     */
    private String object;
}
