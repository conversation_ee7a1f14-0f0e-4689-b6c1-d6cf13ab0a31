package com.sankuai.shangou.seashop.product.core.service.assist.handler;

import lombok.Getter;

/**
 * 处理器配置枚举
 *
 * <AUTHOR>
 * @date 2024/01/26 8:56
 */
@Getter
public enum HandlerConfigEnum {

    /**
     * 保存商品校验
     */
    CHECK_SPEC(-1),
    CHECK_PRODUCT(0),
    CHECK_CATEGORY(1),
    CHECK_BRAND(2),
    CHECK_SHOP_CATEGORY(2),
    CHECK_FREIGHT_TEMPLATE(2),
    CHECK_SKU(3),
    CHECK_PROMOTION(4),
    CHECK_LADDER_PRICE(5),

    /**
     * 查询商品详情
     */
    QUERY_PRODUCT(0),
    QUERY_PRODUCT_IMAGE(1),
    QUERY_PRODUCT_DETAIL(2),
    QUERY_SHOP_CATEGORY(2),
    QUERY_SKU(2),
    QUERY_BRAND(3),
    QUERY_CATEGORY(3),
    QUERY_LADDER_PRICE(5),
    CALCULATE_PRICE_RANGE(6),
    QUERY_FREIGHT_TEMPLATE(7),

    /**
     * 保存商品
     */
    TRANSFER_IMAGE(Integer.MIN_VALUE),
    SAVE_SKU(3),
    SAVE_PRODUCT(1),
    SAVE_PRODUCT_IMAGE(2),
    SAVE_PRODUCT_LADDER_PRICE(6),
    SAVE_PRODUCT_POST(Integer.MAX_VALUE),
    SAVE_PRODUCT_PRE(Integer.MIN_VALUE + 1),
    SAVE_PRODUCT_SHOP_CATEGORY(5),
    SAVE_PRODUCT_SPEC(2),
    SAVE_PRODUCT_MIN_SALE_PRICE(7),

    /**
     * 审核通过
     */
    PASS_PRODUCT_AUDIT(0),
    PASS_PRODUCT_AUDIT_POST(Integer.MAX_VALUE),
    PASS_PRODUCT_DESCRIPTION_AUDIT(1),
    PASS_PRODUCT_IMAGE_AUDIT(2),
    PASS_PRODUCT_LADDER_PRICE_AUDIT(3),
    PASS_SKU_AUDIT(4),

    /**
     * 获取审核详情
     */
    QUERY_PRODUCT_AUDIT(0),
    QUERY_LADDER_PRICE_AUDIT(1),
    QUERY_PRODUCT_DETAIL_AUDIT(1),
    QUERY_PRODUCT_IMAGE_AUDIT(1),
    QUERY_SHOP_CATEGORY_AUDIT(2),
    QUERY_BRAND_AUDIT(3),
    QUERY_CATEGORY_AUDIT(3),
    QUERY_SKU_AUDIT(3),
    CALCULATE_PRICE_RANGE_AUDIT(5),

    /**
     * 提交审核
     */
    REMOVE_HISTORY_AUDIT(-99),
    SUBMIT_PRODUCT_AUDIT(1),
    SUBMIT_PRODUCT_DESCRIPTION_AUDIT(2),
    SUBMIT_PRODUCT_IMAGE_AUDIT(3),
    SUBMIT_SKU_AUDIT(4),
    SUBMIT_LADDER_PRICE_AUDIT(5),
    SUBMIT_PRODUCT_AUDIT_POST(Integer.MAX_VALUE),
    ;


    private int order;

    HandlerConfigEnum(int order) {
        this.order = order;
    }
}
