package com.sankuai.shangou.seashop.user.account.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.account.log.RoleLogBO;
import com.sankuai.shangou.seashop.user.account.service.RoleService;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Role;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.RolePrivilegeRepository;
import com.sankuai.shangou.seashop.user.dao.account.repository.RoleRepository;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleRespList;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Resource
    private RoleRepository roleRepository;
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private RolePrivilegeRepository rolePrivilegeRepository;
    @Resource
    private BaseLogAssist baseLogAssist;


    @Override
    public RoleRespList queryRoleList(QueryRoleReq queryRoleReq) {
        List<Role> Roles = roleRepository.selectListByShopId(queryRoleReq.getShopId());
        return new RoleRespList(JsonUtil.copyList(Roles, RoleResp.class));
    }

    @Override
    public Long addRole(CmdRoleReq cmdRoleReq) {
        //检查角色名是否重复
        Role role1 = new Role();
        role1.setRoleName(cmdRoleReq.getRoleName());
        role1.setShopId(cmdRoleReq.getShopId());
        List<Role> roles = roleRepository.selectList(role1);
        if (roles.size() > 0) {
            throw new BusinessException(UserResultCodeEnum.ROLE_NAME_EXIST);
        }
        Role role = addRoleInfo(cmdRoleReq);
        cmdRoleReq.setId(role.getId());
        baseLogAssist.recordLog(ExaminModelEnum.USER,
            ExaProEnum.INSERT, "添加权限组",
                cmdRoleReq.getOperationUserId(), cmdRoleReq.getOperationShopId(),
                new RoleLogBO(), new RoleLogBO(cmdRoleReq));
        return role.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Role addRoleInfo(CmdRoleReq cmdRoleReq) {
        //保存角色信息
        Role role = new Role();
        role.setRoleName(cmdRoleReq.getRoleName());
        role.setDescription(cmdRoleReq.getRoleName());
        role.setShopId(cmdRoleReq.getShopId());
        role.setCreateTime(new Date());
        role.setUpdateTime(new Date());
        roleRepository.insert(role);
        //保存角色权限关系
        rolePrivilegeRepository.insert(role.getId(), cmdRoleReq.getPrivilegeIds());
        return role;
    }

    @Override
    public Long editRole(CmdRoleReq cmdRoleReq) {
        Role role = roleRepository.selectById(cmdRoleReq.getId());
        if (role == null) {
            throw new BusinessException(UserResultCodeEnum.ROLE_NOT_EXIST);
        }
        updateRole(cmdRoleReq, role);
        return role.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRole(CmdRoleReq cmdRoleReq, Role role) {
        //查询角色权限关系
        List<Integer> privilegeIds = rolePrivilegeRepository.selectPrivilegeIdsByRoleId(role.getId());
        baseLogAssist.recordLog(ExaminModelEnum.USER,
            ExaProEnum.INSERT, "编辑权限组",
                cmdRoleReq.getOperationUserId(), cmdRoleReq.getOperationShopId(),
            new RoleLogBO(role, privilegeIds), new RoleLogBO(cmdRoleReq));
        role.setRoleName(cmdRoleReq.getRoleName());
        role.setUpdateTime(new Date());
        roleRepository.update(role);

        //删除角色权限关系
        rolePrivilegeRepository.deleteByRoleId(role.getId());
        //保存角色权限关系
        rolePrivilegeRepository.insert(role.getId(), cmdRoleReq.getPrivilegeIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteRole(CmdRoleReq cmdRoleReq) {
        //检查管理员是否有该角色
        Manager manager = new Manager();
        manager.setRoleId(cmdRoleReq.getId());
        List<Manager> managers = managerRepository.selectList(manager);
        if (managers.size() > 0) {
            throw new BusinessException(UserResultCodeEnum.THE_ROLE_HAS_ADMIN);
        }
        return deleteRoleInfo(cmdRoleReq);
    }

    private Long deleteRoleInfo(CmdRoleReq cmdRoleReq) {
        //删除角色
        roleRepository.delete(cmdRoleReq.getId());
        //删除角色权限关系
        rolePrivilegeRepository.deleteByRoleId(cmdRoleReq.getId());
        baseLogAssist.recordLog(ExaminModelEnum.USER,
            ExaProEnum.MOVE, "删除权限组",
                cmdRoleReq.getOperationUserId(), cmdRoleReq.getOperationShopId(),
                new RoleLogBO(cmdRoleReq), new RoleLogBO(cmdRoleReq));
        return cmdRoleReq.getId();
    }

}
