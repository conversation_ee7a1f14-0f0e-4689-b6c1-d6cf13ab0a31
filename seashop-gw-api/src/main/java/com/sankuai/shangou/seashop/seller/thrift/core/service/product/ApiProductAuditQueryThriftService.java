package com.sankuai.shangou.seashop.seller.thrift.core.service.product;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiProductQueryDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.product.ApiQueryProductAuditReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductAuditDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.product.ApiProductAuditPageResp;

/**
 * <AUTHOR>
 * @date 2023/12/21 14:27
 */
@InterfaceDoc(
    type = "thrift",
    displayName = "商品审核查询服务",
    description = "商品审核查询服务",
    scenarios = "商品审核查询服务",
    host = ""
)
@ThriftService
public interface ApiProductAuditQueryThriftService {

    @MethodDoc(
        displayName = "分页查询商品审核的集合",
        description = "分页查询商品审核的集合",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "request", description = "查询商品审核入参")
        },
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<BasePageResp<ApiProductAuditPageResp>> queryProductAudit(ApiQueryProductAuditReq request, ShopUserInfo shopInfo) throws TException;

    @MethodDoc(
        displayName = "查询商品审核详情",
        description = "查询商品审核详情",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "request", description = "查询商品详情入参")
        },
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<ApiProductAuditDetailResp> queryProductAuditDetail(ApiProductQueryDetailReq request, ShopUserInfo shopInfo) throws TException;

}
