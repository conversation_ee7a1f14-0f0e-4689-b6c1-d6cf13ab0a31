package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
public enum FlashSaleStatusEnum {

    WAIT_FOR_AUDITING(1, "待审核"),
    ONGOING(2, "进行中"),
    AUDIT_FAILED(3, "未通过"),
    ENDED(4, "已结束"),
    CANCELLED(5, "已取消"),
    NOT_BEGIN(6, "未开始"),
    ;

    private Integer status;
    private String desc;

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByStatus(Integer status) {
        for (FlashSaleStatusEnum value : FlashSaleStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static FlashSaleStatusEnum fromStatusByTime(Date beginTime, Date endTime) {
        Date now = new Date();
        if (now.before(beginTime)) {
            return NOT_BEGIN;
        } else if (now.after(endTime)) {
            return ENDED;
        } else {
            return ONGOING;
        }
    }

    FlashSaleStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
