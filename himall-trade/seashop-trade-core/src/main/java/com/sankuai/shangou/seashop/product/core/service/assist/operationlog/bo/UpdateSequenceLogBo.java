package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:08
 */
@Getter
@Setter
public class UpdateSequenceLogBo {

    @ExaminField(description = "商品列表", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo.UpdateSequenceItemLogBo")
    private List<UpdateSequenceItemLogBo> productList;

    public static UpdateSequenceLogBo build(List<Product> productList) {
        UpdateSequenceLogBo bo = new UpdateSequenceLogBo();
        if (CollectionUtils.isEmpty(productList)) {
            return bo;
        }

        bo.setProductList(JsonUtil.copyList(productList, UpdateSequenceItemLogBo.class));
        return bo;
    }

    public static UpdateSequenceLogBo build(List<Long> productIdList, Long displaySequence) {
        UpdateSequenceLogBo bo = new UpdateSequenceLogBo();
        if (CollectionUtils.isEmpty(productIdList)) {
            return bo;
        }

        List<UpdateSequenceItemLogBo> productList = new ArrayList<>();
        for (Long productId : productIdList) {
            UpdateSequenceItemLogBo itemBo = new UpdateSequenceItemLogBo();
            itemBo.setProductId(productId);
            itemBo.setDisplaySequence(displaySequence);
            productList.add(itemBo);
        }
        bo.setProductList(productList);
        return bo;
    }

}
