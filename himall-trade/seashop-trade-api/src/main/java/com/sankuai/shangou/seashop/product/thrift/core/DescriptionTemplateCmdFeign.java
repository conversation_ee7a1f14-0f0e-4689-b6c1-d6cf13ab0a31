package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.DeleteDescriptionTemplateReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.descriptiontemplate.SaveDescriptionTemplateReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 版式操作服务
 *
 * author HuBiao
 * date 2023/11/20 10:17
 */
@FeignClient(name = "himall-trade", contextId = "DescriptionTemplateCmdFeign", path = "/himall-trade/descriptionTemplate", url = "${himall-trade.dev.url:}")
public interface DescriptionTemplateCmdFeign {

    /**
     * 创建版式模板
     */
    @PostMapping(value = "/createDescriptionTemplate", consumes = "application/json")
    ResultDto<BaseResp> createDescriptionTemplate(@RequestBody SaveDescriptionTemplateReq request) throws TException;

    /**
     * 编辑版式模板
     */
    @PostMapping(value = "/updateDescriptionTemplate", consumes = "application/json")
    ResultDto<BaseResp> updateDescriptionTemplate(@RequestBody SaveDescriptionTemplateReq request) throws TException;

    /**
     * 删除版式模板
     */
    @PostMapping(value = "/deleteDescriptionTemplate", consumes = "application/json")
    ResultDto<BaseResp> deleteDescriptionTemplate(@RequestBody DeleteDescriptionTemplateReq request) throws TException;

}
