package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "专享价活动查询请求对象")
public class ExclusivePriceQueryReq extends BasePageReq {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "专享价活动名称")
    private String name;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺ID列表")
    private List<Long> shopIdList;

    @Schema(description = "状态 0-未开始 1-进行中 2-已结束")
    private Integer status;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;


}
