package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.CategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Category;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.repository.CategoryRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.result.ProductResultEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 类目检查处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 19:41
 */
@Component
@Slf4j
public class CheckCategoryHandler extends AbsProductHandler {

    @Resource
    private CategoryAssist categoryAssist;
    @Resource
    private CategoryRepository categoryRepository;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】校验分类属性【start】, context:{}", context);

        ProductBo productBo = context.getSaveProductBo();
        Product dbProduct = context.getDbProduct();

        Category category;
        // dbProduct 可能为空，因为新增商品时，dbProduct 还没有生成 如果类目没有变化 则不去管权限了
        if (dbProduct != null && dbProduct.getCategoryId().equals(productBo.getCategoryId())) {
            category = categoryRepository.getById(productBo.getCategoryId());
            AssertUtil.throwIfNull(category, ProductResultEnum.CATEGORY_NOT_EXIST);
        }
        else {
            category = categoryAssist.checkCategoryAuth(productBo.getCategoryId(), context.getCurrentShop());
        }

        productBo.setCategoryPath(category.getPath());
        productBo.setCategoryName(category.getName());

        log.info("【保存商品】校验分类属性【end】, context:{}", context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getCategoryId() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_CATEGORY;
    }
}
