package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "退款明细对象")
public class SellerRefundDetailResp extends BaseThriftDto {

    /**
     * 退款id，退款表主键ID
     */
    @FieldDoc(description = "退款ID，退款表主键ID，售后编号")
    private Long refundId;
    /**
     * 订单号
     */
    @FieldDoc(description = "订单号")
    private String orderId;
    /**
     * 退款商品id。退明细时有值
     */
    @FieldDoc(description = "退款商品id。退明细时有值")
    private Long orderItemId;
    /**
     * 售后类型。1：订单退款
     */
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款")
    private Integer refundType;
    /**
     * 售后类型描述
     */
    @FieldDoc(description = "售后类型描述")
    private String refundTypeDesc;
    /**
     * 退款模式。1：订单退款；2：货品退款；3：退货退款
     */
    @FieldDoc(description = "退款模式。1：订单退款；2：货品退款；3：退货退款")
    private Integer refundMode;
    /**
     * 退款模式描述
     */
    @FieldDoc(description = "退款模式描述")
    private String refundModeDesc;
    /**
     * 申请退款金额
     */
    @FieldDoc(description = "申请退款金额")
    private BigDecimal refundAmount;
    /**
     * 退款数量。退货退款时有值
     */
    @FieldDoc(description = "退款数量。退货退款时有值")
    private Long refundQuantity;
    /**
     * 购买数量，整单退是订单所有明细数量，单品退是明细数量
     */
    @FieldDoc(description = "购买数量，整单退是订单所有明细数量，单品退是明细数量")
    private Long orderQuantity;
    /**
     * 运费金额
     */
    @FieldDoc(description = "运费金额")
    private BigDecimal freightAmount;
    /**
     * 订单实付金额，整单退是订单金额，单品退是明细金额
     */
    @FieldDoc(description = "订单实付金额，整单退是订单金额，单品退是明细金额")
    private BigDecimal payAmount;

    /**
     * 退款原因类型
     */
    @FieldDoc(description = "退款原因类型")
    private String refundReasonDesc;
    /**
     * 退款说明
     */
    @FieldDoc(description = "退款说明")
    private String refundRemark;
    /**
     * 联系人姓名
     */
    @FieldDoc(description = "联系人姓名")
    private String contactUserName;
    /**
     * 联系人电话
     */
    @FieldDoc(description = "联系人电话")
    private String contactUserPhone;
    /**
     * 退款方式。1：原路返回
     */
    @FieldDoc(description = "退款方式。1：原路返回")
    private Integer refundPayType;
    /**
     * 退款方式描述
     */
    @FieldDoc(description = "退款方式描述")
    private String refundPayTypeDesc;
    /**
     * 售后凭证1。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证1。最多三张，与数据表保持一致分开")
    private String certPic1;
    /**
     * 售后凭证2。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证2。最多三张，与数据表保持一致分开")
    private String certPic2;
    /**
     * 售后凭证3。最多三张，与数据表保持一致分开
     */
    @FieldDoc(description = "售后凭证3。最多三张，与数据表保持一致分开")
    private String certPic3;
    /**
     * 是否订单全退
     */
    @FieldDoc(description = "是否订单全退")
    private Boolean hasAllReturn;

    /**
     * 是否可以取消
     */
    @FieldDoc(description = "是否可以取消")
    private Boolean hasCancel;
    /**
     * 供应商审核状态。
     */
    @FieldDoc(description = "供应商审核状态。")
    private Integer sellerAuditStatus;
    /**
     * 供应商审核状态描述
     */
    @FieldDoc(description = "供应商审核状态描述")
    private String sellerAuditStatusDesc;
    /**
     * 平台审核状态
     */
    @FieldDoc(description = "平台审核状态")
    private Integer managerConfirmStatus;
    /**
     * 平台审核状态描述
     */
    @FieldDoc(description = "平台审核状态描述")
    private String managerConfirmStatusDesc;
    /**
     * 综合的售后状态
     */
    @FieldDoc(description = "综合的售后状态")
    private Integer status;
    /**
     * 综合的售后状态描述
     */
    @FieldDoc(description = "综合的售后状态描述")
    private String statusDesc;
    /**
     * 供应商备注
     */
    @FieldDoc(description = "供应商备注")
    private String sellerRemark;
    /**
     * 平台备注
     */
    @FieldDoc(description = "平台备注")
    private String platformRemark;
    /**
     * 退款完成日期
     */
    @FieldDoc(description = "退款完成日期")
    private Date refundFinishDate;
    /**
     * 扩展的退款类型描述
     */
    @FieldDoc(description = "扩展的退款类型描述")
    private String extRefundTypeDesc;
    /**
     * 退款明细，如果为null，前端显示【订单所有商品】
     */
    @FieldDoc(description = "退款明细，如果为null，前端显示【订单所有商品】")
    private RefundItemDto item;
    /**
     * 审核截止时间描述
     */
    @FieldDoc(description = "审核截止时间描述")
    private String remainApproveDeadlineDesc;
    /**
     * 审核超时的下一个状态
     */
    @FieldDoc(description = "审核超时的下一个状态")
    private Integer deadlineNextStatus;
    /**
     * 审核超时的下一个状态描述
     */
    @FieldDoc(description = "审核超时的下一个状态描述")
    private String deadlineNextStatusDesc;
    /**
     * 快递公司编码
     */
    @FieldDoc(description = "快递公司编码")
    private String expressCompanyCode;
    /**
     * 快递公司名称
     */
    @FieldDoc(description = "快递公司名称")
    private String expressCompanyName;
    /**
     * 快递单号
     */
    @FieldDoc(description = "快递单号")
    private String shipOrderNumber;
    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "申请时间")
    private Date applyDate;


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public String getFreightAmountString() {
        return this.bigDecimal2String(this.freightAmount);
    }


    public void setFreightAmountString(String freightAmount) {
        this.freightAmount = this.string2BigDecimal(freightAmount);
    }


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


    public Long getRefundFinishDateLong() {
        return this.date2Long(this.refundFinishDate);
    }


    public void setRefundFinishDateLong(Long refundFinishDate) {
        this.refundFinishDate = this.long2Date(refundFinishDate);
    }


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }
}
