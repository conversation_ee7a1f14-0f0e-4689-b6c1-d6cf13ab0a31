package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ExclusivePriceSaveBo  extends BaseParamReq {

    @PrimaryField
    //主键ID
    private Long id;

    //供应商Id
    private Long shopId;

    @ExaminField
    //活动名称
    private String name;

    @ExaminField
    //开始时间
    private Date startTime;

    @ExaminField
    //结束时间
    private Date endTime;

    //商品列表
    private List<ExclusivePriceProductBo> productList;

}
