package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductAuditAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 移除历史审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
public class RemoveHistoryAuditHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private ProductAuditAssist productAuditAssist;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        log.info("【商品提交审核】移除旧的审核记录【start】, productId: {}", productId);

        productAuditAssist.removeHistoryAudit(productId);

        log.info("【商品提交审核】移除旧的审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.REMOVE_HISTORY_AUDIT;
    }

}
