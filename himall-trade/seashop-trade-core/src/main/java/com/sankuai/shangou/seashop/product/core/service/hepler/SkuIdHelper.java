package com.sankuai.shangou.seashop.product.core.service.hepler;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/16 23:01
 */
@Slf4j
public class SkuIdHelper {
    @Getter
    @Setter
    public static class SkuIdDto {

        /**
         * skuId 商品id_规格1Id_规格2Id_规格3Id
         */
        private String skuId;

        /**
         * 规格1 Id
         */
        private Long spec1ValueId;

        /**
         * 规格2 Id
         */
        private Long spec2ValueId;

        /**
         * 规格3 Id
         */
        private Long spec3ValueId;

        /**
         * 商品id
         */
        private Long productId;

    }

    public static SkuIdDto getSkuIdDto(String skuId) {
        try {
            SkuIdDto skuIdDto = new SkuIdDto();
            String[] split = skuId.split(CommonConstant.SKU_ID_SPLIT);
            skuIdDto.setSkuId(skuId);
            skuIdDto.setProductId(Long.valueOf(split[0]));
            skuIdDto.setSpec1ValueId(Long.valueOf(split[1]));
            skuIdDto.setSpec2ValueId(Long.valueOf(split[2]));
            skuIdDto.setSpec3ValueId(Long.valueOf(split[3]));
            return skuIdDto;
        }
        catch (Exception e) {
            log.error("skuId格式错误 skuId={}", skuId, e);
            throw new BusinessException("skuId格式错误");
        }
    }

    public static String getSkuId(SkuIdDto skuIdDto, Long productId) {
        try {
            return String.format(CommonConstant.SKU_ID_FORMAT, productId, skuIdDto.getSpec1ValueId(), skuIdDto.getSpec2ValueId(),
                    skuIdDto.getSpec3ValueId());
        }
        catch (Exception e) {
            log.error("skuId格式错误 skuIdDto={}, productId={}", skuIdDto, productId, e);
            throw new BusinessException("skuId格式错误");
        }
    }

    public static Long getProductId(String skuId) {
        SkuIdDto skuIdDto = getSkuIdDto(skuId);
        return skuIdDto.getProductId();
    }

    public static List<Long> getProductIds(List<String> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return Collections.EMPTY_LIST;
        }

        return skuIdList.stream().map(SkuIdHelper::getProductId).distinct().collect(Collectors.toList());
    }
}
