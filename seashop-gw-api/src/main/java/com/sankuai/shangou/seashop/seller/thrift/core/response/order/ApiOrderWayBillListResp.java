package com.sankuai.shangou.seashop.seller.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单收货信息返回对象")
public class ApiOrderWayBillListResp extends BaseThriftDto {

    @FieldDoc(description = "是否需要物流")
    private Boolean needExpress;
    @FieldDoc(description = "订单物流信息")
    private List<ApiOrderWayBillResp> orderWayBillList;


}
