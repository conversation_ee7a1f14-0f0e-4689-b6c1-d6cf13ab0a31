package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.*;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.collocation.PageCollocationResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * 组合购活动查询相关服务
 *
 * author liweisong
 * date 2023/12/18 11:02
 */
@FeignClient(name = "himall-trade",contextId = "CollocationQueryFeign", path = "/himall-trade/collocation", url = "${himall-trade.dev.url:}")
public interface CollocationQueryFeign {

    /**
     * 组合购活动列表查询(平台端)
     */
    @PostMapping(value = "/pageMCollocation", consumes = "application/json")
    ResultDto<BasePageResp<PageCollocationResp>> pageMCollocation(@RequestBody PageMCollocationReq request) throws TException;

    /**
     * 组合购活动列表查询(供应商端)
     */
    @PostMapping(value = "/pageSellerCollocation", consumes = "application/json")
    ResultDto<BasePageResp<PageCollocationResp>> pageSellerCollocation(@RequestBody PageSellerCollocationReq request) throws TException;

    /**
     * 组合购活动查询详情(平台端、供应商端共用)
     */
    @PostMapping(value = "/queryCollocationDetail", consumes = "application/json")
    ResultDto<CollocationResp> queryCollocationDetail(@RequestBody CollocationDetailReq req) throws TException;

    /**
     * 商品详情-组合购活动分组-买家端
     */
    @PostMapping(value = "/queryMallCollocationList", consumes = "application/json")
    ResultDto<MallCollocationResp> queryMallCollocationList(@RequestBody MallCollocationReq request) throws TException;

    /**
     * 根据商品ID集合查询组合购活动
     */
    @PostMapping(value = "/queryCollocationByProductIdsAndStatus", consumes = "application/json")
    ResultDto<List<CollocationActivityResp>> queryCollocationByProductIdsAndStatus(@RequestBody CollocationActivityReq request) throws TException;
}
