package com.sankuai.shangou.seashop.product.core.service.assist;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.core.service.assist.comparator.FiledComparatorFactory;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/15 17:05
 */
@Slf4j
public class ProductFieldHelper {

    /**
     * 提取类上标记了@ProductField 注解的字段
     * 并根据是否需要审批进行分组
     * 如果无需审批将值放入noAuditFieldMap
     * 需要审批则判断与旧的数据是否一致, 如果不一致则放入auditFieldMap
     *
     * @param newObj     新对象
     * @param oldObj     旧对象
     * @param ignoreNull 是否忽略空值(如果新对象的字段值为null, 则跳过判断, 用于部分更新的场景, 为null则不更新)
     * @return 返回需要入库的字段 true:需要审核的字段map false:不需要审核的字段map
     */
    public static <T> Map<Boolean, Map<String, Object>> getFieldMap(@NonNull T newObj, T oldObj, boolean ignoreNull) {
        Map<String, Object> auditFieldMap = new HashMap<>();
        Map<String, Object> noAuditFieldMap = new HashMap<>();
        try {
            Class<?> curClazz = newObj.getClass();
            Field[] fields = curClazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(Boolean.TRUE);
                ProductField fieldAnno = field.getAnnotation(ProductField.class);
                if (fieldAnno == null) {
                    continue;
                }
                Object newField = field.get(newObj);
                if (newField == null && ignoreNull) {
                    continue;
                }

                if (fieldAnno.needAudit()) {
                    // 对比新旧字段是否一致
                    if (oldObj == null || !FiledComparatorFactory.compare(fieldAnno, newField, field.get(oldObj))) {
                        auditFieldMap.put(field.getName(), newField);
                    }
                }
                else {
                    noAuditFieldMap.put(field.getName(), newField);
                }
            }
        }
        catch (Exception e) {
            log.error("对比新旧字段是否一致异常", e);
            throw new BusinessException("对比新旧字段是否一致异常");
        }

        Map<Boolean, Map<String, Object>> result = new HashMap<>();
        result.put(Boolean.TRUE, auditFieldMap);
        result.put(Boolean.FALSE, noAuditFieldMap);
        return result;
    }

}
