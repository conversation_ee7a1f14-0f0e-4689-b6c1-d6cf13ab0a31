package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.CollocationFlashSaleProductReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ProductIdListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2024/3/7/007
 * @description: 提供营销活动商品查询功能
 */
@FeignClient(name = "himall-trade",contextId = "PromotionProductFeign", path = "/himall-trade/promotionProduct", url = "${himall-trade.dev.url:}")
public interface PromotionProductFeign {

    /**
     * 查询参加限时购、组合购的商品ID列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/collocationFlashSaleProductId", consumes = "application/json")
    ResultDto<ProductIdListResp> collocationFlashSaleProductId(@RequestBody ShopIdReq request) throws TException;

    /**
     * 查询参加限时购、组合购的商品ID列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/collocationFlashSaleProductIdIncludeSub", consumes = "application/json")
    ResultDto<ProductIdListResp> collocationFlashSaleProductIdIncludeSub(@RequestBody CollocationFlashSaleProductReq request) throws TException;
}
