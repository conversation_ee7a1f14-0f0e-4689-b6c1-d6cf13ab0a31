package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "专享价活动保存对象")
public class ExclusivePriceSaveReq extends BaseParamReq {

    @Schema(description = "主键ID")
    @PrimaryField(title = "主键")
    private Long id;

    @Schema(description = "供应商Id", required = true)
    @ExaminField(description = "供应商Id")
    private Long shopId;

    @Schema(description = "活动名称", required = true)
    @ExaminField(description = "活动名称")
    private String name;

    @Schema(description = "开始时间", required = true)
    @ExaminField(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间", required = true)
    @ExaminField(description = "结束时间")
    private Date endTime;

    @Schema(description = "商品列表", required = true)
    private List<ExclusivePriceProductDto> productList;

    @Override
    public void checkParameter() {
        if (this.shopId == null || this.shopId <= 0) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (StringUtils.isBlank(this.name)) {
            throw new InvalidParamException("活动名称不能为空");
        }
        if (this.startTime == null) {
            throw new InvalidParamException("startTime不能为空");
        }
        if (this.endTime == null) {
            throw new InvalidParamException("endTime不能为空");
        }
        if (this.startTime.after(this.endTime)) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
        if (this.endTime.before(new Date())) {
            throw new InvalidParamException("结束时间不能小于当前时间");
        }
        if (CollUtil.isEmpty(this.productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
