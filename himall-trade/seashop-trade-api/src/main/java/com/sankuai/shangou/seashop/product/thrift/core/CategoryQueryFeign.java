package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.*;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryTreeResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/06 11:45
 * @description 类目查询服务
 */
@FeignClient(name = "himall-trade", contextId = "CategoryQueryFeign", path = "/himall-trade/category", url = "${himall-trade.dev.url:}")
public interface CategoryQueryFeign {

    /**
     * 查询类目树
     */
    @PostMapping(value = "/queryCategoryTree", consumes = "application/json")
    ResultDto<CategoryTreeResp> queryCategoryTree(@RequestBody QueryCategoryReq request) throws TException;

    @PostMapping(value = "/queryCategoryTreeHideNoThreeLevel", consumes = "application/json")
    ResultDto<CategoryTreeResp> queryCategoryTreeHideNoThreeLevel(@RequestBody QueryCategoryReq request) throws TException;

    /**
     * 查询类目树(申请类目时使用)
     */
    @PostMapping(value = "/queryCategoryTreeForApply", consumes = "application/json")
    ResultDto<CategoryTreeResp> queryCategoryTreeForApply(@RequestBody QueryCategoryForApplyReq request) throws TException;

    /**
     * 查询类目列表
     */
    @PostMapping(value = "/queryCategoryList", consumes = "application/json")
    ResultDto<CategoryListResp> queryCategoryList(@RequestBody QueryCategoryListReq queryCategoryListReq) throws TException;

    /**
     * 获取当前类目下最后一级的类目(包含保证金信息)
     */
    @PostMapping(value = "/queryLastCategoryList", consumes = "application/json")
    ResultDto<CategoryListResp> queryLastCategoryList(@RequestBody BaseIdReq request) throws TException;

    /**
     * 查询第一级类目的集合
     */
    @PostMapping(value = "/queryFirstCategoryList", consumes = "application/json")
    ResultDto<CategoryListResp> queryFirstCategoryList(@RequestBody QueryFirstCategoryReq request) throws TException;

    /**
     * 查询类目树(补齐上级类目)
     */
    @PostMapping(value = "/queryCategoryTreeWithParent", consumes = "application/json")
    ResultDto<CategoryTreeResp> queryCategoryTreeWithParent(@RequestBody QueryCategoryTreeReq request) throws TException;

    /**
     * 查询类目树(补齐上级类目)
     */
    @PostMapping(value = "/getAllCategoryPath", consumes = "application/json")
    ResultDto<CategoryListResp> getAllCategoryPath(@RequestBody QueryCategoryTreeReq request) throws TException;

    /**
     * 查询类目集合(读取缓存)
     */
    @PostMapping(value = "/getCategoryList", consumes = "application/json")
    ResultDto<CategoryListResp> getCategoryList(@RequestBody QueryCategoryByIdsReq request) throws TException;


    @GetMapping("/queryAlreadyRemoveCategoryIds")
    ResultDto<List<Long>> queryAlreadyRemoveCategoryIds();
}
