package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.ProductAuditReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/17 10:59
 * 商品审核操作服务
 */
@FeignClient(name = "himall-trade", contextId = "ProductAuditCmdFeign", path = "/himall-trade/productAudit", url = "${himall-trade.dev.url:}")
public interface ProductAuditCmdFeign {

    /**
     * 商品审核
     */
    @PostMapping(value = "/batchAuditProduct", consumes = "application/json")
    ResultDto<BaseResp> batchAuditProduct(@RequestBody ProductAuditReq request) throws TException;

}
