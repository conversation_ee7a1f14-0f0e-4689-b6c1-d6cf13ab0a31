package com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "限时购库存退还请求对象")
public class FlashSaleStockReturnReq extends BaseParamReq {

    @Schema(description = "限时购活动ID", required = true)
    private Long flashSaleId;

    @Schema(description = "skuId", required = true)
    private String skuId;

    @Schema(description = "订单ID", required = true)
    private String orderId;

    @Schema(description = "关联ID（售后单ID）", required = true)
    private Long relationId;

    @Schema(description = "退还数量", required = true)
    private Integer returnNum;

    @Override
    public void checkParameter() {
        if (this.flashSaleId == null) {
            throw new InvalidParamException("限时购活动ID不能为空");
        }
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("订单ID不能为空");
        }
        if (this.relationId == null) {
            throw new InvalidParamException("关联ID不能为空");
        }
        if (this.returnNum == null || this.returnNum <= 0) {
            throw new InvalidParamException("退还数量不能为空");
        }
    }


}
