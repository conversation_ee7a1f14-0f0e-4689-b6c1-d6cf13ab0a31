package com.sankuai.shangou.seashop.product.core.service.assist.listener.event;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.transaction.event.TransactionPhase;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.AbstractHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.handler.SendProductChangeHandler;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.ProductChangeEvent;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

/**
 * 商品变动通知事件
 *
 * <AUTHOR>
 * @date 2024/01/03 16:16
 */
@Setter
@Getter
public class SendProductChangeEvent extends AbstractTransactionEvent<List<ProductChangeEvent>> {

    @Override
    public Class<? extends AbstractHandler> getHandler() {
        return SendProductChangeHandler.class;
    }

    @Override
    public TransactionPhase getTransactionPhase() {
        return TransactionPhase.AFTER_COMMIT;
    }

    public static SendProductChangeEvent build(@NonNull Long productId,
                                               @NonNull Long shopId,
                                               ProductSourceEnum source,
                                               @NonNull ProductChangeType changeType) {
        SendProductChangeEvent event = new SendProductChangeEvent();
        ProductChangeEvent productChangeEvent = buildChangeEvent(productId, shopId, source, changeType);
        event.setEventBody(Arrays.asList(productChangeEvent));
        return event;
    }

    public static SendProductChangeEvent build(@NonNull List<Long> productIds,
                                               @NonNull Long shopId,
                                               ProductSourceEnum source,
                                               @NonNull ProductChangeType changeType) {
        List<ProductChangeEvent> events = new ArrayList<>();
        productIds.forEach(productId -> {
            events.add(buildChangeEvent(productId, shopId, source, changeType));
        });

        SendProductChangeEvent sendEvent = new SendProductChangeEvent();
        sendEvent.setEventBody(events);
        return sendEvent;
    }

    public static SendProductChangeEvent build(@NonNull List<Product> products,
                                               ProductSourceEnum source,
                                               @NonNull ProductChangeType changeType) {
        List<ProductChangeEvent> events = new ArrayList<>();
        products.forEach(product -> {
            events.add(buildChangeEvent(product.getProductId(), product.getShopId(), source, changeType));
        });

        SendProductChangeEvent sendEvent = new SendProductChangeEvent();
        sendEvent.setEventBody(events);
        return sendEvent;
    }

    public static ProductChangeEvent buildChangeEvent(@NonNull Long productId,
                                                      @NonNull Long shopId,
                                                      ProductSourceEnum source,
                                                      @NonNull ProductChangeType changeType) {
        ProductChangeEvent productChangeEvent = new ProductChangeEvent();
        productChangeEvent.setProductId(productId);
        productChangeEvent.setShopId(shopId);
        productChangeEvent.setSource(ObjectUtils.defaultIfNull(source, ProductSourceEnum.MALL));
        productChangeEvent.setChangeType(changeType);
        return productChangeEvent;
    }

}
