package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "店铺折扣对象")
public class ShopDiscountDto extends BaseThriftDto {

    @Schema(description = "折扣活动ID")
    private Long discountActId;
    @Schema(description = "折扣活动名称")
    private String discountActName;
    @Schema(description = "是否用于全部商品")
    private Boolean izAllProduct;
    @Schema(description = "折扣规则")
    private List<DiscountActiveRuleReq> ruleList;
    @Schema(description = "活动适用的商品")
    private List<Long> productIdList;
    @Schema(description = "开始时间")
    private Date startTime;
    @Schema(description = "结束时间")
    private Date endTime;


}
