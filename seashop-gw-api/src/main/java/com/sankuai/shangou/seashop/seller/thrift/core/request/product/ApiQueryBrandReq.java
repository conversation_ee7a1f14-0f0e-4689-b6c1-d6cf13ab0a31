package com.sankuai.shangou.seashop.seller.thrift.core.request.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询商家负责的品牌列表")
public class ApiQueryBrandReq extends BasePageReq {

    @FieldDoc(description = "品牌名称")
    private String brandName;

    @FieldDoc(description = "品牌名称 优先级低于brandName")
    private String name;


}
