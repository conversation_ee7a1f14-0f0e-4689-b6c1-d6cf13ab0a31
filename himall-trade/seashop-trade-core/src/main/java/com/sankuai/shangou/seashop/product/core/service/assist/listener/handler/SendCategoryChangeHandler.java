package com.sankuai.shangou.seashop.product.core.service.assist.listener.handler;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.mq.publisher.CategoryChangePublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendCategoryChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/05/05 14:53
 */
@Slf4j
@Component
public class SendCategoryChangeHandler extends AbstractHandler<SendCategoryChangeEvent>{

    @Resource
    private CategoryChangePublisher categoryChangePublisher;

    @Override
    public void handle(SendCategoryChangeEvent body) {
        categoryChangePublisher.sendMessage(body.getEventBody());
    }
}
