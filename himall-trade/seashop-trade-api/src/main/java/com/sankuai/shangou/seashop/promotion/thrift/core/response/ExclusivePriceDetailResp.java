package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ExclusivePriceProductDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/25/025
 * @description: 目前主要提供给导出使用
 */
@Schema(description = "专享价活动（包含专项的详细信息）响应体")
@Data
public class ExclusivePriceDetailResp extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "专享价明细信息")
    private List<ExclusivePriceProductDto> productList;


}
