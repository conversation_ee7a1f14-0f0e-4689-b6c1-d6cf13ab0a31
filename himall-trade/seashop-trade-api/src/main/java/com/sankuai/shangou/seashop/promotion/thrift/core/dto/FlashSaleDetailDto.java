package com.sankuai.shangou.seashop.promotion.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "新增限时购活动请求对象")
public class FlashSaleDetailDto extends BaseParamReq {

    /**
     * 规格id
     */
    @Schema(description = "规格id", required = true)
    private String skuId;

    /**
     * 限时购时金额
     */
    @Schema(description = "限时购时金额", required = true)
    private BigDecimal price;

    /**
     * 活动库存
     */
    @Schema(description = "活动库存", required = true)
    private Integer totalCount;

    /**
     * 限购数量
     */
    @Schema(description = "限购数量")
    private Integer limitCount;

    @Override
    public void valueInit() {
        if (null == this.limitCount) {
            // 默认不限购
            this.limitCount = 0;
        }
    }

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("规格id不能为空");
        }
        if (this.price == null || this.price.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("限时购时金额不能为空");
        }
        if (this.totalCount == null || this.totalCount < 0) {
            throw new InvalidParamException("活动库存不能为空");
        }
    }


}
