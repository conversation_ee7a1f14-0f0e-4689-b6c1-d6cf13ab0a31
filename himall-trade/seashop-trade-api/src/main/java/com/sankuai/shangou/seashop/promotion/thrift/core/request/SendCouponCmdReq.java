package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Schema(description = "会员操作请求对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendCouponCmdReq extends BaseParamReq {

    @Schema(description = "优惠券ID列表")
    @ExaminField(description = "优惠券ID列表")
    private List<Long> couponIds;

    @Schema(description = "会员ID列表")
    @ExaminField(description = "会员ID列表")
    private List<Long> memberIds;

    @Schema(description = "消息ID")
    @ExaminField(description = "消息ID")
    private Long msgId;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(this.couponIds)) {
            throw new InvalidParamException("优惠券ID列表不能为空");
        }
        if (CollUtil.isEmpty(this.memberIds)) {
            throw new InvalidParamException("会员ID列表不能为空");
        }
    }


}
