package com.sankuai.shangou.seashop.product.core.service.assist;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductParam;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductAuditService;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerProcessor;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.converter.EsProductConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductPageBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImageAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPriceAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductDescriptionAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductImageAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockAuditRepository;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/11/24 17:05
 */
@Component
public class ProductAuditAssist {

    @Resource
    private ProductHandlerProcessor productHandlerProcessor;
    @Resource
    private ProductAuditRepository productAuditRepository;
    @Resource
    private ProductExtAssist productExtAssist;
    @Resource
    private EsProductAuditService esProductAuditService;
    @Resource
    private ProductDescriptionAuditRepository productDescriptionAuditRepository;
    @Resource
    private ProductImageAuditRepository productImageAuditRepository;
    @Resource
    private ProductLadderPriceAuditRepository productLadderPriceAuditRepository;
    @Resource
    private SkuAuditRepository skuAuditRepository;
    @Resource
    private SkuStockAuditRepository skuStockAuditRepository;

    /**
     * 从es中查询商品列表
     *
     * @param pageParam 分页参数
     * @param queryBo   查询参数
     * @return 商品列表
     */
    public BasePageResp<ProductPageBo> pageEsProduct(BasePageParam pageParam, ProductQueryBo queryBo) {
        BasePageResp<EsProductModel> esProductResult = esProductAuditService.page(pageParam, JsonUtil.copy(queryBo, EsProductParam.class), queryBo.getSortList());
        BasePageResp<ProductPageBo> resp = PageResultHelper.transfer(esProductResult, model -> EsProductConverter.esModelToBo(model));
        List<ProductPageBo> data = resp.getData();
        productExtAssist.fillProductExtData(queryBo, data, true);
        return resp;
    }

    /**
     * 审核通过
     *
     * @param productId 商品id
     */
    public void passAudit(Long productId) {
        productHandlerProcessor.handle(ProductHandlerType.PASS_PRODUCT_AUDIT, ProductContext.builder().productId(productId).build());
    }

    /**
     * 检测店铺权限并且查询商品审核集合
     *
     * @param productIds         商品id的集合
     * @param shopId             店铺id
     * @param ignoreIfShopIdNull 当shopId为空是是否忽略鉴权
     * @return 店铺审核集合
     */
    public List<ProductAudit> checkShopAndListProductAudit(List<Long> productIds, Long shopId, boolean ignoreIfShopIdNull) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        List<ProductAudit> auditList = productAuditRepository.listByProductIds(productIds);
        if (ignoreIfShopIdNull && shopId == null) {
            return auditList;
        }
        long count = auditList.stream().filter(product -> !product.getShopId().equals(shopId)).count();
        AssertUtil.throwInvalidParamIfTrue(count > 0, "存在无权商品");
        return auditList;
    }

    /**
     * 检测店铺权限并且查询商品审核集合
     *
     * @param productIds 商品id的集合
     * @param shopId     店铺id
     * @return 店铺审核集合
     */
    public List<ProductAudit> checkShopAndListProductAudit(List<Long> productIds, Long shopId) {
        return checkShopAndListProductAudit(productIds, shopId, Boolean.TRUE);
    }

    /**
     * 移除历史审核
     *
     * @param productId 商品id
     */
    public void removeHistoryAudit(Long productId) {
        // 如果不存在审核记录则不需要移除
        /*if (!existAudit(productId)) {
            return;
        }*/
        TransactionHelper.doInTransaction(() -> {
            productAuditRepository.remove(new LambdaQueryWrapper<ProductAudit>().eq(ProductAudit::getProductId, productId));
            productDescriptionAuditRepository.remove(new LambdaQueryWrapper<ProductDescriptionAudit>().eq(ProductDescriptionAudit::getProductId, productId));
            productImageAuditRepository.remove(new LambdaQueryWrapper<ProductImageAudit>().eq(ProductImageAudit::getProductId, productId));
            productLadderPriceAuditRepository.remove(new LambdaQueryWrapper<ProductLadderPriceAudit>().eq(ProductLadderPriceAudit::getProductId, productId));
            skuAuditRepository.remove(new LambdaQueryWrapper<SkuAudit>().eq(SkuAudit::getProductId, productId));
            skuStockAuditRepository.remove(new LambdaQueryWrapper<SkuStockAudit>().eq(SkuStockAudit::getProductId, productId));
        });
    }

    /**
     * 移除历史审核
     *
     * @param productIds 商品id集合
     */
    public void removeHistoryAudit(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }
        TransactionHelper.doInTransaction(() -> {
            MybatisUtil.executeBatch(ids -> {
                productAuditRepository.remove(new LambdaQueryWrapper<ProductAudit>().in(ProductAudit::getProductId, ids));
                productDescriptionAuditRepository.remove(new LambdaQueryWrapper<ProductDescriptionAudit>().in(ProductDescriptionAudit::getProductId, ids));
                productImageAuditRepository.remove(new LambdaQueryWrapper<ProductImageAudit>().in(ProductImageAudit::getProductId, ids));
                productLadderPriceAuditRepository.remove(new LambdaQueryWrapper<ProductLadderPriceAudit>().in(ProductLadderPriceAudit::getProductId, ids));
                skuAuditRepository.remove(new LambdaQueryWrapper<SkuAudit>().in(SkuAudit::getProductId, ids));
                skuStockAuditRepository.remove(new LambdaQueryWrapper<SkuStockAudit>().in(SkuStockAudit::getProductId, ids));
            }, productIds);
        });
    }

    /**
     * 是否存在审核记录
     *
     * @param productId 商品id
     * @return 是否存在 true:存在 false:不存在
     */
    public boolean existAudit(Long productId) {
        return productAuditRepository.count(new LambdaQueryWrapper<ProductAudit>().eq(ProductAudit::getProductId, productId)) > 0;
    }

    /**
     * 获取审核原因
     *
     * @param productId 商品id
     * @return 审核原因
     */
    public String getAuditReason(Long productId) {
        ProductDescriptionAudit desc = productDescriptionAuditRepository.getProductDescriptionByProductId(productId);
        return desc == null ? null : desc.getAuditReason();
    }

    /**
     * 拼接审核原因
     *
     * @param productAudit 商品审核信息
     * @return 审核原因
     */
    public String getAuditReason(ProductAudit productAudit) {
        List<String> auditReasonList = new ArrayList<>();
        String auditReason = getAuditReason(productAudit.getProductId());
        if (StringUtils.isNotEmpty(auditReason)) {
            auditReasonList.add(auditReason);
        }
        return StrUtil.join(";", auditReasonList.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 计算最大售价
     *
     * @param productId 商品id
     * @return 最大售价
     */
    public BigDecimal calculateMaxSalePrice(Long productId) {
        BigDecimal maxLadderPrice = productLadderPriceAuditRepository.getMaxLadderPrice(productId);
        if (maxLadderPrice != null) {
            return maxLadderPrice;
        }

        return skuAuditRepository.getMaxSalePrice(productId);
    }
}
