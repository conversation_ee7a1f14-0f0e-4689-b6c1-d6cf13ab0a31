package com.sankuai.shangou.seashop.seller.thrift.core.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-11-16
 */
@Data
@ToString
@TypeDoc(description = "专属商家")
public class ApiExclusiveMemberResp extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "商家ID")
    private Long userId;

    @FieldDoc(description = "新增时间")
    private Date addDate;

    @FieldDoc(description = "商家账号")
    private String userName;


    public Long getAddDateLong() {
        return this.date2Long(this.addDate);
    }


    public void setAddDateLong(Long addDate) {
        this.addDate = this.long2Date(addDate);
    }


}
