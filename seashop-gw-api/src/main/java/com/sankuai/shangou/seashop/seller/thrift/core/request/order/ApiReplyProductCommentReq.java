package com.sankuai.shangou.seashop.seller.thrift.core.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:37
 */
@Data
@ToString
@TypeDoc(description = "回复商品评论入参")
public class ApiReplyProductCommentReq extends BaseParamReq {

    @FieldDoc(description = "商品评论id", requiredness = Requiredness.REQUIRED)
    private Long productCommentId;

    @FieldDoc(description = "回复内容 ", requiredness = Requiredness.NONE)
    private String replyContent;

    @FieldDoc(description = "追加回复内容", requiredness = Requiredness.NONE)
    private String replyAppendContent;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productCommentId == null || productCommentId <= 0, "商品评论id不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isNotEmpty(replyContent) && replyContent.length() > 1000, "回复内容不能超过1000个字符");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isNotEmpty(replyAppendContent) && replyAppendContent.length() > 1000, "追加回复内容不能超过1000个字符");
    }


}
