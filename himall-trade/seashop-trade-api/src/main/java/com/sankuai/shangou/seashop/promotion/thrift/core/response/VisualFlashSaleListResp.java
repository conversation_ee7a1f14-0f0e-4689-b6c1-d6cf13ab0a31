package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/16/016
 * @description:
 */
@Schema(description = "限时购集合活动响应体")
@Data
public class VisualFlashSaleListResp extends BaseThriftDto {

    @Schema(description = "限时购活动集合")
    private List<VisualFlashSaleResp> flashSaleList;


}
