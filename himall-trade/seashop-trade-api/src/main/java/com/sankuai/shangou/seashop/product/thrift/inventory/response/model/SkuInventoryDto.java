package com.sankuai.shangou.seashop.product.thrift.inventory.response.model;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/06 15:15
 */
@Schema(description = "规格库存dto")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class SkuInventoryDto extends BaseThriftDto {

    @Schema(description = "商品id")
    private Long productId;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "库存")
    private Long inventory;

}
