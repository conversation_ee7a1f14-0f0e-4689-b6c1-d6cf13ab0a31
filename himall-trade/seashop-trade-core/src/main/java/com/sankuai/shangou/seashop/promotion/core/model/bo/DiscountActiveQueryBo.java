package com.sankuai.shangou.seashop.promotion.core.model.bo;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class DiscountActiveQueryBo extends BasePageReq {

    //主键ID
    private Long id;

    //店铺ID
    private Long shopId;

    //店铺ID列表
    private List<Long> shopIdList;

    //活动名称
    private String activeName;

    //开始时间
    private Date startTime;

    //结束时间
    private Date endTime;

    //状态
    private Integer status;

}
