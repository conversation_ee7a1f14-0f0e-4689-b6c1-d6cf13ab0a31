package com.sankuai.shangou.seashop.seller.thrift.core.response.product;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @description：
 * @author： liweisong
 * @create： 2023/12/11 9:37
 */
@Data
@TypeDoc(description = "供应商首页统计商品信息")
public class ApiStatisticalProductResp extends BaseParamReq {
    @FieldDoc(description = "发布商品数量")
    private Integer productsNumber;

    @FieldDoc(description = "出售中")
    private Integer productsOnSale;

    @FieldDoc(description = "草稿箱")
    private Integer productsInDraft;

    @FieldDoc(description = "待审核")
    private Integer productsWaitForAuditing;

    @FieldDoc(description = "审核未通过")
    private Integer productsAuditFailed;

    @FieldDoc(description = "违规下架")
    private Integer productsInfractionSaleOff;

    @FieldDoc(description = "仓库中")
    private Integer productsInStock;

    @FieldDoc(description = "警戒库存数")
    private Integer overSafeStockProducts;

    @FieldDoc(description = "授权品牌")
    private Integer productsBrands;


}
