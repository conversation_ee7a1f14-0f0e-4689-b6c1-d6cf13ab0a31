package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.submit;

import javax.annotation.Resource;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.converter.ProductConverter;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 提交商品审核
 *
 * <AUTHOR>
 * @date 2023/11/17 13:40
 */
@Component
@Slf4j
@Order(1)
public class SubmitProductAuditHandler extends AbsSubmitProductAuditHandler {

    @Resource
    private ProductAssist productAssist;
    @Resource
    private ProductAuditRepository productAuditRepository;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        log.info("【商品提交审核】保存商品审核记录【start】, productId: {}", productId);

        ProductBo auditProductBo = context.getAuditProductBo();
        Product product = ProductConverter.convertToEntity(auditProductBo, context.getShopId(), context.isDraftFlag());
        ProductBo oldProductBo = context.getOldProductBo();
        if (oldProductBo != null && oldProductBo.getAddedDate()!= null) {
            product.setAddedDate(oldProductBo.getAddedDate());
        }
        product.setMinSalePrice(productAssist.getMinSalePrice(auditProductBo));
        ProductAudit productAudit = JsonUtil.copy(product, ProductAudit.class);
        productAudit.setAuditStatus(ProductEnum.AuditStatusEnum.WAIT_AUDIT.getCode());
        productAuditRepository.save(productAudit);

        log.info("【商品提交审核】保存商品审核记录【end】, productId: {}", productId);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SUBMIT_PRODUCT_AUDIT;
    }


}
