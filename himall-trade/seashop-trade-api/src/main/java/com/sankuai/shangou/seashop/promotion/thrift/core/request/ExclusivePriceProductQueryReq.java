package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;


/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "专项价查询请求对象")
public class ExclusivePriceProductQueryReq extends BaseParamReq {

    @Schema(description = "会员id", required = true)
    private Long memberId;

    @Schema(description = "skuId列表", required = true)
    private List<String> skuIdList;

    @Override
    public void checkParameter() {
        if (this.memberId == null || this.memberId <= 0) {
            throw new InvalidParamException("memberId不能为空");
        }
        if (CollectionUtils.isEmpty(skuIdList)) {
            throw new InvalidParamException("skuIdList不能为空");
        }
    }


}
