package com.sankuai.shangou.seashop.product.core.service.assist.listener.handler;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.SyncStockMessage;
import com.sankuai.shangou.seashop.product.core.mq.publisher.SyncStockPublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendSyncStockEvent;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTaskInfo;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockTaskInfoRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/25 14:18
 */
@Slf4j
@Component
public class SendSyncStockHandler extends AbstractHandler<SendSyncStockEvent> {

    @Resource
    private SyncStockPublisher syncStockPublisher;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private SkuStockTaskInfoRepository skuStockTaskInfoRepository;


    @Override
    public void handle(SendSyncStockEvent event) {
        List<StockTaskInfoBo> taskInfoList = event.getEventBody();
        if (CollectionUtils.isEmpty(taskInfoList)) {
            return;
        }

        Map<Long, List<StockTaskInfoBo>> taskInfoMap = taskInfoList.stream().collect(Collectors.groupingBy(StockTaskInfoBo::getProductId));
        Set<Long> productIds = taskInfoMap.keySet();
        productIds.forEach(productId -> {
            List<StockTaskInfoBo> taskInfoBoList = taskInfoMap.get(productId);
            sendMessage(taskInfoBoList, productId);
        });
    }

    private void sendMessage(List<StockTaskInfoBo> taskInfoList, Long productId) {
        List<List<StockTaskInfoBo>> taskInfosArr = Lists.partition(taskInfoList, CommonConstant.DISTRIBUTED_LOCK_MAX_SIZE);

        taskInfosArr.forEach(subTaskInfoList -> {
            // 已经限制小于50
            List<LockKey> lockKeys = getLockKeys(subTaskInfoList);
            distributedLockService.tryMultiReentrantLock(lockKeys, () -> {
                log.info("同步库存， 获取库存同步锁成功，开始处理任务, lockKeys: {}", JsonUtil.toJsonString(lockKeys));

                TransactionHelper.doInTransaction(() -> {
                    // 将任务标记为执行中
                    List<Long> taskIds = subTaskInfoList.stream().map(StockTaskInfoBo::getId).collect(Collectors.toList());
                    SkuStockTaskInfo updTaskInfo = new SkuStockTaskInfo();
                    updTaskInfo.setStatus(StockUpdateStatusEnum.EXECUTING.getCode());
                    // ids 最多50，不会超过限制
                    skuStockTaskInfoRepository.update(updTaskInfo,
                            new LambdaQueryWrapper<>(SkuStockTaskInfo.class)
                                    .in(SkuStockTaskInfo::getId, taskIds).eq(SkuStockTaskInfo::getStatus, StockUpdateStatusEnum.WAIT.getCode()));
                });

                // 发送库存同步消息 根据productId分区
                syncStockPublisher.sendMessage(JsonUtil.copyList(subTaskInfoList, SyncStockMessage.class), productId);
                log.info("同步库存， 发送消息成功, taskInfoList: {}", JsonUtil.toJsonString(subTaskInfoList));
            });
        });
    }

    private List<LockKey> getLockKeys(List<StockTaskInfoBo> taskInfoList) {
        List<LockKey> lockKeys = Lists.newArrayList();
        taskInfoList.forEach(taskInfo -> {
            LockKey lockKey = LockKey.builder()
                    .catTag(LockConstant.STOCK_TASK_SCENE + taskInfo.getId())
                    .lockName(LockConstant.STOCK_TASK_LOCK + taskInfo.getId())
                    .build();
            lockKeys.add(lockKey);
        });
        return lockKeys;
    }

}
