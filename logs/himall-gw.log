2025-07-30 11:50:01.108 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 11:50:01.300 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [55] -| Starting GatewayApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 7744 (E:\work\himallWork\himall-gw\seashop-gw-server\target\classes started by Admin in E:\work\himallWork)
2025-07-30 11:50:01.313 |-DEBUG [main][] -  com.sankuai.shangou.seashop.GatewayApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-30 11:50:01.321 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-07-30 11:50:01.863 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-gw.yml, group=1.0.0] success
2025-07-30 11:50:01.863 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-30 11:50:04.950 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-30 11:50:08.812 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 11:50:08.915 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-30 11:50:10.176 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-30 11:50:12.072 |-INFO  [redisson-netty-1-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 11:50:14.408 |-INFO  [redisson-netty-1-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 11:50:20.335 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-30 11:50:25.012 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-gw) init on namesrv 124.71.221.178:9876
2025-07-30 11:50:33.085 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:50:35.178 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:50:41.747 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:50:41.747 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:MExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 11:50:41.751 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:50:43.876 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:50:50.351 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:50:50.352 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:sendMessageListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 11:50:55.021 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-30 11:50:56.748 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:50:58.714 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:51:04.777 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:51:04.777 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:mallExportTaskListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 11:51:07.309 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-30 11:51:07.509 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-30 11:51:12.895 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-30 11:51:12.911 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-30 11:51:14.573 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:7744, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-30 11:51:15.485 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=458, lastTimeStamp=1753847474581}] - instanceId:[InstanceId{instanceId=*******:7744, stable=false}] - machineBit:[20] @ namespace:[himall-gw].
2025-07-30 11:51:15.640 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-gw.__share__].
2025-07-30 11:51:15.642 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 11:51:15.643 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-gw.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 11:51:15.643 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-gw.__share__] jobSize:[0].
2025-07-30 11:51:23.313 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-30 11:51:26.598 |-INFO  [Thread-183][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-30 11:51:26.766 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-30 11:51:26.843 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-30 11:51:26.881 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 11:51:27.059 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-30 11:51:27.269 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [61] -| Started GatewayApplication in 93.26 seconds (JVM running for 97.413)
2025-07-30 11:51:27.290 |-INFO  [main][] -  com.sankuai.shangou.seashop.GatewayApplication [40] -| 服务启动成功！
2025-07-30 11:51:27.575 |-INFO  [task-3][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 11:51:27.575 |-INFO  [task-3][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 11:51:27.753 |-INFO  [task-1][79d8c6f1f3385887] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-30 11:51:27.755 |-INFO  [task-1][79d8c6f1f3385887] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-gw.yml, group=1.0.0
2025-07-30 11:51:28.951 |-INFO  [task-3][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-gw *******:8081 register finished
2025-07-30 11:51:30.631 |-INFO  [RMI TCP Connection(9)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:51:33.747 |-WARN  [RMI TCP Connection(7)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-30 11:53:00.386 |-INFO  [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 11:53:00.592 |-INFO  [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 11:53:00.595 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 11:53:00.619 |-INFO  [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 11:53:00.703 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 11:53:00.703 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 11:53:00.703 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 11:53:00.703 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 11:53:00.703 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 11:53:00.704 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 11:53:13.080 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (12375ms)
2025-07-30 11:53:13.081 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 11:53:13.081 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 11:53:13.081 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 03:53:12 GMT
2025-07-30 11:53:13.081 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: fc6a959e4fd3a02c
2025-07-30 11:53:13.081 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 11:53:13.082 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 11:53:13.082 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 11:53:13.082 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 11:53:13.082 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 11:53:13.088 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":500,"message":"服务端异常","success":false}
2025-07-30 11:53:13.088 |-DEBUG [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (68-byte body)
2025-07-30 11:53:13.102 |-WARN  [XNIO-1 task-1][fc6a959e4fd3a02c] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 11:53:39.514 |-INFO  [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 11:53:39.677 |-INFO  [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 11:53:39.677 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 11:53:39.679 |-INFO  [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 11:53:39.682 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 11:53:39.682 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 11:53:39.682 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 11:53:39.682 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 11:53:39.682 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 11:53:39.682 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 11:53:47.601 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (7918ms)
2025-07-30 11:53:47.602 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 11:53:47.602 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 11:53:47.602 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 03:53:47 GMT
2025-07-30 11:53:47.602 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: e71a4aafd4345daf
2025-07-30 11:53:47.602 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 11:53:47.603 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 11:53:47.603 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 11:53:47.603 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 11:53:47.603 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 11:53:47.603 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":500,"message":"服务端异常","success":false}
2025-07-30 11:53:47.603 |-DEBUG [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (68-byte body)
2025-07-30 11:53:47.605 |-WARN  [XNIO-1 task-1][e71a4aafd4345daf] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 13:37:15.296 |-INFO  [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 13:37:15.341 |-INFO  [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 13:37:15.342 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 13:37:15.343 |-INFO  [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 13:37:15.344 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 13:37:15.344 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 13:37:15.344 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 13:37:15.344 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:37:15.344 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 13:37:15.345 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 13:37:27.434 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (12088ms)
2025-07-30 13:37:27.434 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 13:37:27.434 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 13:37:27.434 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 05:37:27 GMT
2025-07-30 13:37:27.434 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: bcbfb716b6a62674
2025-07-30 13:37:27.435 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 13:37:27.435 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 13:37:27.435 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 13:37:27.435 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 13:37:27.435 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:37:27.435 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 13:37:27.436 |-DEBUG [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 13:37:27.436 |-WARN  [XNIO-1 task-1][bcbfb716b6a62674] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 13:38:30.408 |-INFO  [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 13:38:30.465 |-INFO  [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 13:38:30.465 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 13:38:30.467 |-INFO  [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 13:38:30.467 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 13:38:30.468 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 13:38:30.468 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 13:38:30.468 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:38:30.468 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 13:38:30.468 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 13:38:34.263 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (3794ms)
2025-07-30 13:38:34.264 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 13:38:34.264 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 13:38:34.264 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 05:38:34 GMT
2025-07-30 13:38:34.264 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: dfc308aea3a7b9df
2025-07-30 13:38:34.264 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 13:38:34.265 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 13:38:34.265 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 13:38:34.265 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 13:38:34.265 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:38:34.265 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 13:38:34.266 |-DEBUG [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 13:38:34.266 |-WARN  [XNIO-1 task-1][dfc308aea3a7b9df] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 13:42:12.740 |-INFO  [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 13:42:12.813 |-INFO  [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 13:42:12.813 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 13:42:12.814 |-INFO  [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 13:42:17.031 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 13:42:17.032 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 13:42:17.032 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 13:42:17.033 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:42:17.034 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 13:42:17.034 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 13:43:17.060 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- ERROR SocketTimeoutException: timeout (60024ms)
2025-07-30 13:43:17.067 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146)
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323)
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at com.sun.proxy.$Proxy184.importProduct(Unknown Source)
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.lambda$importProduct$31(SellerProductRemoteService.java:265)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60)
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	... 133 more

2025-07-30 13:43:17.068 |-DEBUG [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END ERROR
2025-07-30 13:43:50.867 |-ERROR [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: timeout executing POST http://localhost:8084/himall-trade/product/importProduct
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy184.importProduct(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.lambda$importProduct$31(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_121]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_121]
	at okio.InputStreamSource.read(JvmOkio.kt:93) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
2025-07-30 13:43:50.875 |-INFO  [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 13:43:50.877 |-WARN  [XNIO-1 task-1][0d206d0fa69cda15] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 13:43:51.047 |-INFO  [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 13:43:51.048 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 13:43:51.048 |-INFO  [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 13:43:52.002 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 13:43:52.003 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 13:43:52.004 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 13:43:52.004 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:43:52.004 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 13:43:52.005 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 13:44:16.437 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (24431ms)
2025-07-30 13:44:16.437 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 13:44:16.438 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 13:44:16.438 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 05:44:16 GMT
2025-07-30 13:44:16.438 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: fdd24c15bbeef63a
2025-07-30 13:44:16.439 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 13:44:16.439 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 13:44:16.440 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 13:44:16.440 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 13:44:16.440 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:44:16.440 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 13:44:16.440 |-DEBUG [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 13:44:16.440 |-WARN  [XNIO-1 task-2][fdd24c15bbeef63a] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/68899451e4b0a8665f46c3bf.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 13:45:56.191 |-INFO  [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 13:45:56.247 |-INFO  [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 13:45:56.247 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 13:45:56.248 |-INFO  [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 13:45:58.980 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 13:45:58.981 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 13:45:58.981 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 13:45:58.981 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:45:58.981 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 13:45:58.981 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 13:46:02.788 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (3806ms)
2025-07-30 13:46:02.788 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 13:46:02.788 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 13:46:02.789 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 05:46:02 GMT
2025-07-30 13:46:02.789 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: b467368cece9b5ec
2025-07-30 13:46:02.789 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 13:46:02.789 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 13:46:02.789 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 13:46:02.789 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 13:46:02.790 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:46:02.790 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 13:46:02.790 |-DEBUG [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 13:46:02.791 |-WARN  [XNIO-1 task-2][b467368cece9b5ec] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 13:46:39.826 |-INFO  [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 13:46:39.962 |-INFO  [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 13:46:39.965 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 13:46:39.965 |-INFO  [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 13:46:39.966 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 13:46:39.967 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 13:46:39.967 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 13:46:39.967 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:46:39.967 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 13:46:39.967 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 13:46:43.699 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (3731ms)
2025-07-30 13:46:43.700 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 13:46:43.700 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 13:46:43.700 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 05:46:43 GMT
2025-07-30 13:46:43.701 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: e6f1186d7d0381ac
2025-07-30 13:46:43.701 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 13:46:43.701 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 13:46:43.702 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 13:46:43.702 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 13:46:43.702 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 13:46:43.703 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 13:46:43.703 |-DEBUG [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 13:46:43.703 |-WARN  [XNIO-1 task-2][e6f1186d7d0381ac] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:03:14.712 |-INFO  [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 14:03:14.975 |-INFO  [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 14:03:14.975 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 14:03:14.976 |-INFO  [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 14:03:14.977 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 14:03:14.977 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 14:03:14.977 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 14:03:14.977 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:03:14.977 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 14:03:14.977 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 14:03:21.077 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (6099ms)
2025-07-30 14:03:21.077 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 06:03:21 GMT
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: d0f668018217a811
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 14:03:21.078 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:03:21.079 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 14:03:21.079 |-DEBUG [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 14:03:21.079 |-WARN  [XNIO-1 task-2][d0f668018217a811] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:17:30.608 |-INFO  [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 14:17:30.764 |-INFO  [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 14:17:30.765 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 14:17:30.765 |-INFO  [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 14:17:30.766 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 14:17:30.766 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 14:17:30.766 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 14:17:30.766 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:17:30.767 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 14:17:30.767 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (13493ms)
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 06:17:44 GMT
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: 129b71b377e5fa04
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 14:17:44.262 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 14:17:44.263 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 14:17:44.263 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 14:17:44.263 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:17:44.263 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 14:17:44.263 |-DEBUG [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 14:17:44.264 |-WARN  [XNIO-1 task-2][129b71b377e5fa04] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:28:51.334 |-INFO  [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 14:28:51.487 |-INFO  [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 14:28:51.488 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 14:28:51.490 |-INFO  [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 14:28:51.491 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 14:28:51.491 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 14:28:51.492 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 14:28:51.492 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:28:51.492 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 14:28:51.492 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 14:29:01.368 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (9875ms)
2025-07-30 14:29:01.368 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 14:29:01.368 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 14:29:01.369 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 06:29:01 GMT
2025-07-30 14:29:01.369 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: 4eb3b76641201fab
2025-07-30 14:29:01.369 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 14:29:01.369 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 14:29:01.370 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 14:29:01.370 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 14:29:01.370 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:29:01.370 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":null,"code":-1,"message":"解析文件失败","success":false}
2025-07-30 14:29:01.371 |-DEBUG [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (70-byte body)
2025-07-30 14:29:01.371 |-WARN  [XNIO-1 task-2][4eb3b76641201fab] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 解析文件失败
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:75) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:35:30.741 |-INFO  [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 14:35:30.792 |-INFO  [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 14:35:30.792 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 14:35:30.793 |-INFO  [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 14:35:30.794 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 14:35:30.794 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 14:35:30.795 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 14:35:30.795 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:35:30.795 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 14:35:30.795 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 14:35:50.299 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- HTTP/1.1 200 OK (19503ms)
2025-07-30 14:35:50.299 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] connection: keep-alive
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] content-type: application/json
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] date: Wed, 30 Jul 2025 06:35:50 GMT
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] traceid: ef0043b8084ba6fa
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] transfer-encoding: chunked
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Origin
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Method
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] vary: Access-Control-Request-Headers
2025-07-30 14:35:50.300 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:35:50.301 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"data":{"successCount":13,"errCount":8,"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/himall-base/rs/himall-trade/export/excel/1ff05605-a10d-4f77-ab69-39bf4cf1b1f9/商品导入错误数据2025-07-30_14_35.xlsx"},"code":0,"message":null,"success":true}
2025-07-30 14:35:50.301 |-DEBUG [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END HTTP (267-byte body)
2025-07-30 14:35:50.302 |-INFO  [XNIO-1 task-2][ef0043b8084ba6fa] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [31] -| importProduct success. 请求结果. response={"data":{"successCount":13,"errCount":8,"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/himall-base/rs/himall-trade/export/excel/1ff05605-a10d-4f77-ab69-39bf4cf1b1f9/商品导入错误数据2025-07-30_14_35.xlsx"},"code":0,"message":null}
2025-07-30 14:36:28.731 |-INFO  [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [35] -| token校验通过，获取上下文为空
2025-07-30 14:36:28.886 |-INFO  [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.security.utils.TracerUtil [88] -| 设置用户信息到上下文：{"managerId":712,"managerName":"selleradmin","shopId":162,"roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"],"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"whetherDelete":false,"haveNotPassword":true}
2025-07-30 14:36:28.886 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.security.aop.LoginAspect [151] -| 登录信息:{"id":162,"name":"官方自营店","roleType":"SHOP","disable":false,"shopId":162,"whetherDelete":false,"haveNotPassword":true,"managerId":712,"managerName":"selleradmin","roles":[138,142,143],"privileges":["/","/product/manage","/product/manage","/product/audit","/product/category","/product/brand","/product/commentManage","/product/import","/trade/manage","/trade/manage","/trade/refund","/trade/returnProduct","/trade/tradeComplaints","/trade/ExceptionOrder","/trade/tradeSettings","/trade/paymentManage","/member/manage","/member/manage","/member/tag","/shop/manage","/shop/manage","/shop/deposit","/shop/enter","/shop/financeManage","/decorate/pcMall","/decorate/pcMall","/decorate/pc-style","/decorate/mobileMall","/decorate/mobile-style/theme","/decorate/articleCategory","/decorate/articleManage","/decorate/imageLibrary","/decorate/miniProgramSetting","/decorate/wechatSetting","/decorate/publicMenu","/marketing/guide","/marketing/combinationPurchase","/marketing/discount","/marketing/fullReduction","/marketing/popUpAds","/marketing/flashSale","/marketing/supplierCoupon","/marketing/exclusivePrice","/statistics/shop","/statistics/shop","/statistics/member","/statistics/product","/statistics/trade","/statistics/flow","/statistics/customReport","/statistics/recommendReport","/statistics/downloadReport","/setting/siteSetting","/setting/siteSetting","/setting/admin","/setting/permissionGroup","/setting/expressSettings","/setting/message-config","/setting/product","/setting/operationLog","/setting/customForm","/setting/imageLibrary"]}
2025-07-30 14:36:28.887 |-INFO  [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [28] -| importProduct 请求入参. request={"filePath":"https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","saleStatus":1,"fields":[{"name":"序号","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"*OE号","operationUserId":null,"operationShopId":0},{"name":"零件名称","fieldName":"*商品名称","operationUserId":null,"operationShopId":0},{"name":"采购价","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"出库价","fieldName":"*商城价","operationUserId":null,"operationShopId":0},{"name":"状态","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"主键","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"车型","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"厂牌","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"零件编码","fieldName":"","operationUserId":null,"operationShopId":0},{"name":"库存","fieldName":"*库存","operationUserId":null,"operationShopId":0}],"operationUserId":712,"operationShopId":162}
2025-07-30 14:36:28.887 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> POST http://localhost:8084/himall-trade/product/importProduct HTTP/1.1
2025-07-30 14:36:28.888 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Length: 823
2025-07-30 14:36:28.888 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] Content-Type: application/json
2025-07-30 14:36:28.888 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] 
2025-07-30 14:36:28.888 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] {"operationUserId":712,"operationShopId":162,"filePath":"https://himall-obs.35hiw.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx","shopId":162,"saleStatus":1,"fields":[{"operationShopId":0,"name":"序号","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":"*OE号"},{"operationShopId":0,"name":"零件名称","fieldName":"*商品名称"},{"operationShopId":0,"name":"采购价","fieldName":""},{"operationShopId":0,"name":"出库价","fieldName":"*商城价"},{"operationShopId":0,"name":"状态","fieldName":""},{"operationShopId":0,"name":"主键","fieldName":""},{"operationShopId":0,"name":"车型","fieldName":""},{"operationShopId":0,"name":"厂牌","fieldName":""},{"operationShopId":0,"name":"零件编码","fieldName":""},{"operationShopId":0,"name":"库存","fieldName":"*库存"}]}
2025-07-30 14:36:28.890 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] ---> END HTTP (823-byte body)
2025-07-30 14:37:28.893 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- ERROR SocketTimeoutException: timeout (60003ms)
2025-07-30 14:37:28.895 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146)
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339)
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430)
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323)
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29)
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180)
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110)
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79)
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99)
	at com.sun.proxy.$Proxy184.importProduct(Unknown Source)
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.lambda$importProduct$31(SellerProductRemoteService.java:265)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60)
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281)
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:745)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at okio.InputStreamSource.read(JvmOkio.kt:93)
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
	... 133 more

2025-07-30 14:37:28.896 |-DEBUG [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign [72] -| [ProductCmdFeign#importProduct] <--- END ERROR
2025-07-30 14:37:28.896 |-ERROR [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [68] -| thrift call failed
feign.RetryableException: timeout executing POST http://localhost:8084/himall-trade/product/importProduct
	at feign.FeignException.errorExecuting(FeignException.java:278) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:110) ~[feign-core-13.2.1.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:70) ~[feign-core-13.2.1.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:99) ~[feign-core-13.2.1.jar:?]
	at com.sun.proxy.$Proxy184.importProduct(Unknown Source) ~[?:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.lambda$importProduct$31(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:60) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
Caused by: java.net.SocketTimeoutException: timeout
	at okio.SocketAsyncTimeout.newTimeoutException(JvmOkio.kt:146) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout.access$newTimeoutException(AsyncTimeout.kt:161) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:339) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method) ~[?:1.8.0_121]
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:171) ~[?:1.8.0_121]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_121]
	at okio.InputStreamSource.read(JvmOkio.kt:93) ~[okio-jvm-3.6.0.jar:?]
	at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.indexOf(RealBufferedSource.kt:430) ~[okio-jvm-3.6.0.jar:?]
	at okio.RealBufferedSource.readUtf8LineStrict(RealBufferedSource.kt:323) ~[okio-jvm-3.6.0.jar:?]
	at okhttp3.internal.http1.HeadersReader.readLine(HeadersReader.kt:29) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http1.Http1ExchangeCodec.readResponseHeaders(Http1ExchangeCodec.kt:180) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.Exchange.readResponseHeaders(Exchange.kt:110) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.CallServerInterceptor.intercept(CallServerInterceptor.kt:93) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:34) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201) ~[okhttp-4.12.0.jar:?]
	at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154) ~[okhttp-4.12.0.jar:?]
	at feign.okhttp.OkHttpClient.execute(OkHttpClient.java:177) ~[feign-okhttp-13.2.1.jar:?]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.TracingFeignClient.execute(TracingFeignClient.java:79) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.instrument.web.client.feign.LazyTracingFeignClient.execute(LazyTracingFeignClient.java:62) ~[spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:100) ~[feign-core-13.2.1.jar:?]
	... 112 more
2025-07-30 14:37:28.897 |-WARN  [XNIO-1 task-2][f404c153e868dd90] -  com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper [45] -| importProduct business error. request=ApiProductImportReq(filePath=https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com/chengpei/rs/himall-base/6889b16ae4b0a8665f46c3c0.xlsx, saleStatus=1, fields=[ApiFieldDto(name=序号, fieldName=), ApiFieldDto(name=零件编码, fieldName=*OE号), ApiFieldDto(name=零件名称, fieldName=*商品名称), ApiFieldDto(name=采购价, fieldName=), ApiFieldDto(name=出库价, fieldName=*商城价), ApiFieldDto(name=状态, fieldName=), ApiFieldDto(name=主键, fieldName=), ApiFieldDto(name=车型, fieldName=), ApiFieldDto(name=厂牌, fieldName=), ApiFieldDto(name=零件编码, fieldName=), ApiFieldDto(name=库存, fieldName=*库存)])
com.sankuai.shangou.seashop.base.boot.exception.BusinessException: 服务端异常
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.executeThriftCall(ThriftResponseHelper.java:69) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.common.remote.product.SellerProductRemoteService.importProduct(SellerProductRemoteService.java:265) ~[classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.lambda$importProduct$14(SellerApiProductCmdController.java:281) ~[classes/:?]
	at com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper.responseInvoke(ThriftResponseHelper.java:29) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController.importProduct(SellerApiProductCmdController.java:276) [classes/:?]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$FastClassBySpringCGLIB$$3445cbb5.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.base.security.aop.LoginAspect.around(LoginAspect.java:83) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) [spring-aop-5.3.33.jar:5.3.33]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) [spring-aop-5.3.33.jar:5.3.33]
	at com.sankuai.shangou.seashop.seller.core.thrift.impl.product.SellerApiProductCmdController$$EnhancerBySpringCGLIB$$e1f7e5c7.importProduct(<generated>) [classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_121]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_121]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_121]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_121]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.33.jar:5.3.33]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:523) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.33.jar:5.3.33]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:590) [jakarta.servlet-api-4.0.3.jar:4.0.3]
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:67) [spring-webmvc-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at com.sankuai.shangou.seashop.base.boot.filter.TraceIdFilter.doFilter(TraceIdFilter.java:33) [classes/:?]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:89) [spring-cloud-sleuth-instrumentation-3.1.10.jar:3.1.10]
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131) [spring-cloud-sleuth-autoconfigure-3.1.10.jar:3.1.10]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.33.jar:5.3.33]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.33.jar:5.3.33]
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100) [undertow-servlet-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852) [undertow-core-2.2.28.Final.jar:2.2.28.Final]
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449) [jboss-threads-3.1.0.Final.jar:3.1.0.Final]
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282) [xnio-api-3.8.7.Final.jar:3.8.7.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:39:11.025 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-30 14:39:11.025 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 14:39:11.025 |-INFO  [Thread-162][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 14:39:11.025 |-WARN  [Thread-9][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-30 14:39:11.025 |-INFO  [Thread-162][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:11.025 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.026 |-INFO  [Thread-163][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.030 |-WARN  [Thread-6][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-30 14:39:11.047 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=458, lastTimeStamp=1753857551047}] instanceId:[InstanceId{instanceId=*******:7744, stable=false}] @ namespace:[himall-gw].
2025-07-30 14:39:11.081 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:14.110 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-30 14:39:14.120 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 14:39:14.331 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:14.331 |-INFO  [Thread-183][] -  com.xxl.job.core.server.EmbedServer [91] -| >>>>>>>>>>> xxl-job remoting server stop.
2025-07-30 14:39:14.649 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [87] -| >>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='chengpei_himall-gw', registryValue='http://*******:7200/'}, registryResult:ReturnT [code=200, msg=null, content=null]
2025-07-30 14:39:14.650 |-INFO  [xxl-job, executor ExecutorRegistryThread][] -  com.xxl.job.core.thread.ExecutorRegistryThread [105] -| >>>>>>>>>>> xxl-job, executor registry thread destroy.
2025-07-30 14:39:14.650 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 14:39:14.650 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-30 14:39:14.652 |-INFO  [Thread-182][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-30 14:39:14.652 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-30 14:39:14.788 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-30 14:39:14.802 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_seller_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_seller_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:39:14.803 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_send_message_task_platform_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_send_message_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:39:14.803 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_platform_async_task_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_platform_async_task_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
