package com.sankuai.shangou.seashop.product.core.service;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyAuditBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplySaveBo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:08
 */
public interface BrandApplyService {

    /**
     * 新增/编辑申请品牌
     *
     * @param brandApplySaveBo 申请品牌入参
     */
    void saveBrandApply(BrandApplySaveBo brandApplySaveBo);

    /**
     * 分页查询品牌申请
     *
     * @param basePageParam 分页参数
     * @param queryBo       筛选条件
     * @return 分页对象
     */
    BasePageResp<BrandApplyBo> pageBrandApply(BasePageParam basePageParam, BrandApplyQueryBo queryBo);

    /**
     * 根据id查询品牌申请详情(供应商端)
     *
     * @param id     品牌申请id
     * @param shopId 店铺id
     * @return 品牌申请详情
     */
    BrandApplyBo queryBrandApplyDetailForSeller(Long id, Long shopId);

    /**
     * 根据id查询品牌申请详情(平台端)
     *
     * @param id 品牌申请id
     * @return 品牌申请详情
     */
    BrandApplyBo queryBrandApplyDetailForPlatForm(Long id);

    /**
     * 审核品牌申请
     *
     * @param auditBo 审核入参
     */
    void auditBrandApply(BrandApplyAuditBo auditBo);

    /**
     * 删除品牌申请
     *
     * @param id 品牌申请id
     */
    void deleteBrandApply(Long id);

    /**
     * 校验品牌名称
     *
     * @param brandName 品牌名称
     * @return 校验结果
     */
    String checkBrandName(String brandName);

    /**
     * 查询品牌申请列表
     * @param brandIds
     * @return
     */
    List<BrandApplyBo> queryBrandApplyList(List<Long> brandIds);
}
