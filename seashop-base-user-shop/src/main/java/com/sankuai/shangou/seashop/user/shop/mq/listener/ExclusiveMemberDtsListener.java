package com.sankuai.shangou.seashop.user.shop.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.common.constant.MafkaDtsConstant;
import com.sankuai.shangou.seashop.user.shop.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.user.shop.mq.model.ExclusiveMemberMessage;
import com.sankuai.shangou.seashop.user.shop.service.EsShopBuildService;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/4/004
 * @description:
 */
@Slf4j
@Component
/*@MafkaConsumer(namespace = MafkaDtsConstant.DEFAULT_NAMESPACE,
        topic = MafkaDtsConstant.TOPIC_USER_EXCLUSIVE_MEMBER_DTS,
        group = MafkaDtsConstant.CONSUMER_USER_EXCLUSIVE_MEMBER_DTS)*/
@RocketMQMessageListener(topic = MafkaDtsConstant.TOPIC_USER_EXCLUSIVE_MEMBER_DTS + "_${spring.profiles.active}"
        , consumerGroup = MafkaDtsConstant.CONSUMER_USER_EXCLUSIVE_MEMBER_DTS + "_${spring.profiles.active}")
public class ExclusiveMemberDtsListener implements RocketMQListener<MessageExt> {

    @Resource
    private EsShopBuildService esShopBuildService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【店铺品牌表变更】消息内容为: {}", body);
//        String body = (String) mafkaMessage.getBody();
        try {
            DbTableDataChangeMessage<ExclusiveMemberMessage> messageWrapper = JsonUtil.parseObject(body,
                new TypeReference<DbTableDataChangeMessage<ExclusiveMemberMessage>>() {
                });
            Long shopId = messageWrapper.getData().getShopId();
            esShopBuildService.buildByExclusiveMember(shopId);
        }
        catch (Exception e) {
            log.error("【mafka消费】【店铺品牌表变更】消息处理失败, 消息内容为: {}", body, e);
//            return ConsumeStatus.RECONSUME_LATER;
            throw new RuntimeException(e);
        }
    }
}
