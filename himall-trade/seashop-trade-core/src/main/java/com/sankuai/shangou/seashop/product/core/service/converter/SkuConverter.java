package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.hepler.SkuIdHelper;
import com.sankuai.shangou.seashop.product.core.service.model.erp.ErpSkuBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/11/16 22:57
 */
public class SkuConverter {

    @SuppressWarnings("all")
    public static ProductSkuBo convertToBProductSkuBo(Sku sku, SkuStock skuStock) {
        return Optional.ofNullable(sku).map(item -> {
            ProductSkuBo skuBo = JsonUtil.copy(item, ProductSkuBo.class);

            SkuIdHelper.SkuIdDto skuIdDto = SkuIdHelper.getSkuIdDto(item.getSkuId());
            skuBo.setSkuAutoId(item.getId());
            skuBo.setSkuId(skuIdDto.getSkuId());
            skuBo.setSpec1ValueId(skuIdDto.getSpec1ValueId());
            skuBo.setSpec2ValueId(skuIdDto.getSpec2ValueId());
            skuBo.setSpec3ValueId(skuIdDto.getSpec3ValueId());
            if (skuStock != null) {
                skuBo.setStock(skuStock.getStock());
                skuBo.setSafeStock(skuStock.getSafeStock());
                skuBo.setSkuStockId(skuStock.getId());
            }
            return skuBo;
        }).orElse(null);
    }

    public static Sku convertToSku(ProductSkuBo productSkuBo, Long productId, Long shopId) {
        return Optional.ofNullable(productSkuBo).map(item -> {
            Sku sku = JsonUtil.copy(productSkuBo, Sku.class);
            sku.setId(item.getSkuAutoId());
            sku.setShopId(shopId);
            sku.setProductId(productId);
            return sku;
        }).orElse(null);
    }

    public static List<Sku> convertToSku(List<ProductSkuBo> productSkuBoList, Long productId, Long shopId) {
        if (CollectionUtils.isEmpty(productSkuBoList)) {
            return Collections.EMPTY_LIST;
        }

        List<Sku> skuList = new ArrayList<>();
        productSkuBoList.forEach(bo -> {
            skuList.add(convertToSku(bo, productId, shopId));
        });
        return skuList;
    }

    public static ErpSkuBo convertToErpSkuBo(Sku sku, SkuStock skuStock) {
        return Optional.ofNullable(sku).map(item -> {
            ErpSkuBo skuBo = JsonUtil.copy(item, ErpSkuBo.class);
            skuBo.setSkuName(getSkuName(sku));
            skuBo.setSpecValue(skuBo.getSkuName());
            if (skuStock != null) {
                skuBo.setStock(skuStock.getStock());
            }
            skuBo.setSkuAutoId(item.getId());
            return skuBo;
        }).orElse(null);
    }

    public static List<ErpSkuBo> convertToErpSkuBos(List<Sku> skus, List<SkuStock> skuStocks) {
        if (CollectionUtils.isEmpty(skus)) {
            return Collections.EMPTY_LIST;
        }

        Map<Long, SkuStock> stockMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuStocks)) {
            stockMap = skuStocks.stream().collect(Collectors.toMap(SkuStock::getSkuAutoId, Function.identity()));
        }

        List<ErpSkuBo> skuBos = new ArrayList<>();
        for (Sku sku : skus) {
            skuBos.add(convertToErpSkuBo(sku, stockMap.get(sku.getId())));
        }
        return skuBos;
    }

    private static String getSkuName(Sku sku) {
        if (sku == null) {
            return StrUtil.EMPTY;
        }
        StringBuilder skuName = new StringBuilder();
        if (sku.getSpec1Value() != null) {
            skuName.append(sku.getSpec1Value());
        }
        if (sku.getSpec2Value() != null) {
            skuName.append(sku.getSpec2Value());
        }
        if (sku.getSpec3Value() != null) {
            skuName.append(sku.getSpec3Value());
        }
        return skuName.toString();
    }

}
