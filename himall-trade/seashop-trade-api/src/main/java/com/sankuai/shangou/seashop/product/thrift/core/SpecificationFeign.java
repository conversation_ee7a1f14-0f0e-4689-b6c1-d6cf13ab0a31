package com.sankuai.shangou.seashop.product.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateValueReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecificationReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationValueResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "himall-trade", contextId = "SpecificationFeign", path = "/himall-trade/specification", url = "${himall-trade.dev.url:}")
public interface SpecificationFeign {


    @PostMapping(value = "/query", consumes = "application/json")
    ResultDto<BasePageResp<SpecificationResp>> query(@RequestBody SpecificationReq request) throws TException;

    @PostMapping(value = "/create", consumes = "application/json")
    ResultDto<BaseResp> create(@RequestBody SpecificationReq request) throws TException;

    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody SpecificationReq request) throws TException;

    @PostMapping(value = "/remove", consumes = "application/json")
    ResultDto<BaseResp> remove(@RequestParam Long shopId, @RequestParam Long nameId) throws TException;

    @PostMapping(value = "/createName", consumes = "application/json")
    ResultDto<Long> createName(@RequestBody CreateNameReq request) throws TException;

    @PostMapping(value = "/createValue", consumes = "application/json")
    ResultDto<Long> createValue(@RequestBody CreateValueReq request) throws TException;

    @GetMapping(value = "/names", consumes = "application/json")
    ResultDto<List<SpecificationNameResp>> getNames(@RequestParam Long shopId) throws TException;

    @GetMapping(value = "/values", consumes = "application/json")
    ResultDto<List<SpecificationValueResp>> getValues(@RequestParam Long nameId) throws TException;
}
