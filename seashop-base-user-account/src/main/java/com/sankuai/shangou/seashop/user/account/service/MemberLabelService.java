package com.sankuai.shangou.seashop.user.account.service;


import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.user.dao.account.domain.MemberLabel;
import com.sankuai.shangou.seashop.user.thrift.account.request.BatchCmdMemberLabelReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdMemberReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
public interface MemberLabelService {

    List<MemberLabel> getMemberLabel(MemberLabel memberLabel);

    void deleteMemberLabel(MemberLabel memberLabel);

    void setMemberLabel(CmdMemberReq cmdMemberReq);

    BaseResp batchAddMemberLabel(BatchCmdMemberLabelReq cmdMemberReq);

    void setMemberLabelByUserIdList(List<MemberResp> subList);
}
