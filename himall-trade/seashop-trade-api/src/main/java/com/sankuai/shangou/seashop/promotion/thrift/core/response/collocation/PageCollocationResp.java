package com.sankuai.shangou.seashop.promotion.thrift.core.response.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:07
 */
@Data
@Schema(description = "组合购查询列表返参")
public class PageCollocationResp extends BaseThriftDto {

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "主商品ID")
    private String mainProductId;

    @Schema(description = "主商品名称")
    private String mainProductName;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "状态翻译")
    private String statusName;


}
