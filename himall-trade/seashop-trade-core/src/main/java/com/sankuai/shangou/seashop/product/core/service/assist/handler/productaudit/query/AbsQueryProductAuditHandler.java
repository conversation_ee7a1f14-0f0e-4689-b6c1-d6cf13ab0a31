package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import java.util.Arrays;
import java.util.List;

import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;

/**
 * <AUTHOR>
 * @date 2023/11/23 19:41
 */
public abstract class AbsQueryProductAuditHandler extends AbsProductHandler {

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.QUERY_PRODUCT_AUDIT);
    }
}
