package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:07
 */
@Data
@Schema(description = "组合购查询列表入参(供应商端)")
public class PageSellerCollocationReq extends BasePageReq {

    @Schema(description = "店铺ID（供应商只能查自己店铺的组合购活动）")
    private Long shopId;

    @Schema(description = "组合购活动名称")
    private String title;

    @Schema(description = "组合购活动状态,0未开始，1进行中，2已结束")
    private Integer status;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "商品ID")
    private Long productId;

    @Schema(description = "主商品名称")
    private String mainProductName;


}
