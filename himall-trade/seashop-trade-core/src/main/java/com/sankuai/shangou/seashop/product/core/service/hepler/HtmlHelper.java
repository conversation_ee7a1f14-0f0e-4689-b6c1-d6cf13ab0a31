package com.sankuai.shangou.seashop.product.core.service.hepler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/12/08 13:43
 */
public class HtmlHelper {

    private static final String IMG_TAG_PATTERN = "<img\\b[^<>]*?\\bsrc[\\s\\t\\r\\n]*=[\\s\\t\\r\\n]*[\"']?[\\s\\t\\r\\n]*(?<imgUrl>[^\\s\\t\\r\\n\"'<>]*)[^<>]*?/?[\\s\\t\\r\\n]*>";
    private static final String VIDEO_TAG_PATTERN = "<iframe\\b[^<>]*?\\bsrc[\\s\\t\\r\\n]*=[\\s\\t\\r\\n]*[\"']?[\\s\\t\\r\\n]*(?<videoUrl>[^\\s\\t\\r\\n\"'<>]*)[^<>]*?/?[\\s\\t\\r\\n]*>";

    private static final List<String> SUPPORTED_VIDEO_EXTENSIONS = Arrays.asList("avi", "wmv", "mpg", "mov", "rm", "ram", "swf", "flv", "mp4");

    private static final String HTML_TAG_REGEX = "<[^>]+>";
    private static final String HTML_ENTITY_REGEX = "&[^;]+;";
    private static final String PROTOCOL_HTTP = "http://";
    private static final String PROTOCOL_HTTPS = "https://";
    private static final String QUERY_STRING_SEPARATOR = "\\?";
    private static final String FILE_EXTENSION_SEPARATOR = "\\.";
    private static final String IMG_URL_GROUP = "imgUrl";
    private static final String VIDEO_URL_GROUP = "videoUrl";

    public static List<String> getHtmlImageUrlList(String htmlText) {
        List<String> imageUrls = new ArrayList<>();
        Pattern pattern = Pattern.compile(IMG_TAG_PATTERN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlText);

        while (matcher.find()) {
            imageUrls.add(matcher.group(IMG_URL_GROUP));
        }

        return imageUrls;
    }

    public static List<String> getHtmlVideoUrlList(String htmlText) {
        List<String> videoUrls = new ArrayList<>();
        Pattern pattern = Pattern.compile(VIDEO_TAG_PATTERN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlText);

        while (matcher.find()) {
            String value = matcher.group(VIDEO_URL_GROUP);
            if (isVideoPath(value)) {
                videoUrls.add(value);
            }
        }

        return videoUrls;
    }

    public static String getTextFromHtml(String source) {
        if (StringUtils.isEmpty(source)) {
            return source;
        }

        String target = Pattern.compile(HTML_TAG_REGEX).matcher(source).replaceAll(StrUtil.EMPTY);
        target = Pattern.compile(HTML_ENTITY_REGEX).matcher(target).replaceAll(StrUtil.EMPTY);
        return target;
    }

    private static boolean isVideoPath(String videoUrl) {
        if (StringUtils.isEmpty(videoUrl)) {
            return false;
        }

        String text = videoUrl.toLowerCase().trim();
        if (!text.startsWith(PROTOCOL_HTTP) && !text.startsWith(PROTOCOL_HTTPS)) {
            return false;
        }

        String[] array = text.split(QUERY_STRING_SEPARATOR)[0].split(FILE_EXTENSION_SEPARATOR);
        String extension = array[array.length - 1];
        return SUPPORTED_VIDEO_EXTENSIONS.contains(extension);
    }
}