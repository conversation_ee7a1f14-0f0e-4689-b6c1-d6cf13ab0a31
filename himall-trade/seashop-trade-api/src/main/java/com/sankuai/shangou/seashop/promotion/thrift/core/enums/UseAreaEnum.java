package com.sankuai.shangou.seashop.promotion.thrift.core.enums;

/**
 * @author: l<PERSON><PERSON>
 * @date: 2023/11/8/008
 * @description:
 */
public enum UseAreaEnum {

    ALL(0, "全场通用"),
    PRODUCT(1, "指定商品"),

    ;

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    UseAreaEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
