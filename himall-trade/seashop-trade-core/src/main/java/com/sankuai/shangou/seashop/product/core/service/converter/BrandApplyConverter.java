package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplyBo;
import com.sankuai.shangou.seashop.product.core.service.model.BrandApplySaveBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.BrandApply;
import com.sankuai.shangou.seashop.product.thrift.core.enums.BrandEnum;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/11/07 16:20
 */
public class BrandApplyConverter {

    private static final String[] IGNORE_FIELDS = {"auditStatus", "applyMode"};

    public static BrandApply convert(BrandApplySaveBo brandApplySaveBo) {
        return Optional.ofNullable(brandApplySaveBo).map(bo -> {
            BrandApply brandApply = new BrandApply();
            brandApply.setId(bo.getId());
            brandApply.setApplyMode(Optional.ofNullable(bo.getApplyMode()).map(BrandEnum.ApplyModeEnum::getCode).orElse(null));
            brandApply.setBrandId(bo.getBrandId());
            brandApply.setBrandName(bo.getBrandName());
            brandApply.setDescription(bo.getDescription());
            brandApply.setLogo(bo.getLogo());
            brandApply.setRemark(bo.getRemark());
            brandApply.setAuthCertificate(String.join(",", bo.getAuthCertificateList()));
            return brandApply;
        }).orElse(null);
    }

    public static BrandApplyBo convertToBo(BrandApply brandApply) {
        return Optional.ofNullable(brandApply).map(apply -> {
            BrandApplyBo bo = JsonUtil.copy(apply, BrandApplyBo.class, IGNORE_FIELDS);

            // 授权证书字符串转数组
            bo.setAuthCertificateList(StringUtils.isEmpty(brandApply.getAuthCertificate())
                    ? Collections.emptyList() : Arrays.asList(brandApply.getAuthCertificate().split(StrUtil.COMMA)));

            // 审核状态枚举转换
            BrandEnum.AuditStatusEnum auditStatus = BrandEnum.AuditStatusEnum.getByCode(apply.getAuditStatus());
            if (auditStatus != null) {
                bo.setAuditStatus(auditStatus);
                bo.setAuditStatusCode(auditStatus.getCode());
                bo.setAuditStatusDesc(auditStatus.getDesc());
            }

            // 申请类型枚举转换
            BrandEnum.ApplyModeEnum applyMode = BrandEnum.ApplyModeEnum.getByCode(apply.getApplyMode());
            if (applyMode != null) {
                bo.setApplyMode(applyMode);
                bo.setApplyModeCode(applyMode.getCode());
                bo.setApplyModeDesc(applyMode.getDesc());
            }
            return bo;
        }).orElse(null);
    }
}
