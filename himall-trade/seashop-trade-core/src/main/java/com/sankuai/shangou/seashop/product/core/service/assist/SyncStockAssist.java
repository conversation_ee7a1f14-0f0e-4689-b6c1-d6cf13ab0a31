package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.SkuStockService;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;

import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:19
 */
@Component
public class SyncStockAssist {

    @Resource
    private SkuStockService skuStockService;

    /**
     * 覆盖库存
     *
     * @param shopId         店铺id
     * @param updateTypeEnum 更新类型
     * @param updateKeyEnum  更新key类型
     * @param skuStockList   库存列表
     */
    public void coverStock(Long shopId,
                           String bizCode,
                           StockUpdateTypeEnum updateTypeEnum,
                           StockUpdateKeyEnum updateKeyEnum,
                           List<SkuStock> skuStockList) {
        if (CollectionUtils.isEmpty(skuStockList)) {
            return;
        }

        StockTaskBo taskBo = new StockTaskBo();
        taskBo.setBizCode(bizCode);
        taskBo.setUpdateType(updateTypeEnum);
        taskBo.setUpdateWay(StockUpdateWayEnum.COVER);
        taskBo.setUpdateKey(updateKeyEnum);
        List<StockTaskInfoBo> taskInfoBoList = JsonUtil.copyList(skuStockList, StockTaskInfoBo.class, (source, target) -> {
            target.setShopId(shopId);
            target.setId(null);
        });

        taskBo.setTaskInfoBoList(taskInfoBoList);
        skuStockService.asyncChangeSkuStock(taskBo);
    }

    /**
     * 编辑商品覆盖库存
     *
     * @param shopId       店铺id
     * @param skuStockList 库存列表
     */
    public void coverStockForEditProduct(Long shopId,
                                         List<SkuStock> skuStockList) {
        coverStock(shopId,
            IdUtil.fastUUID(),
            StockUpdateTypeEnum.EDIT_PRODUCT,
            StockUpdateKeyEnum.SKU_ID, skuStockList);
    }

}
