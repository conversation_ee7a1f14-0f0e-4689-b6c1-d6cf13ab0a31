package com.sankuai.shangou.seashop.m.thrift.system.setting.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@TypeDoc(description = "协议对象")
@Data
public class ApiBaseAllAgreementReq extends BaseThriftDto {
    private Long id;

    @FieldDoc(description = "最后修改时间")
    private Date lastUpdateTime;

    @FieldDoc(description = "买家协议内容", requiredness = Requiredness.REQUIRED)
    private String buyerAgreementContent;

    @FieldDoc(description = "卖家协议内容", requiredness = Requiredness.REQUIRED)
    private String sellerAgreementContent;

    @FieldDoc(description = "隐私协议内容", requiredness = Requiredness.REQUIRED)
    private String privacyAgreementContent;

    public void checkParameter() {

        if (StringUtils.isEmpty(this.buyerAgreementContent)) {
            throw new IllegalArgumentException("买家协议内容不能为空");
        }
        if (StringUtils.isEmpty(this.sellerAgreementContent)) {
            throw new IllegalArgumentException("卖家协议内容不能为空");
        }
        if (StringUtils.isEmpty(this.privacyAgreementContent)) {
            throw new IllegalArgumentException("隐私协议内容不能为空");
        }
    }


    public Long getLastUpdateTimeLong() {
        return this.date2Long(this.lastUpdateTime);
    }


    public void setLastUpdateTimeLong(Long lastUpdateTime) {
        this.lastUpdateTime = this.long2Date(lastUpdateTime);
    }


}
