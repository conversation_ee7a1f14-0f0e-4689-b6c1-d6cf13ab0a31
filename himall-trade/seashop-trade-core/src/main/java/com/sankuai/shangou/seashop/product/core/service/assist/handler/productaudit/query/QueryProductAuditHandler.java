package com.sankuai.shangou.seashop.product.core.service.assist.handler.productaudit.query;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 查询商品审核处理器
 *
 * <AUTHOR>
 * @date 2023/11/16 17:08
 */
@Component
@Slf4j
public class QueryProductAuditHandler extends AbsQueryProductAuditHandler {

    @Resource
    private ProductAuditRepository productAuditRepository;

    @Override
    protected void handle(ProductContext context) {
        Long productId = context.getProductId();

        // 查询商品基本信息
        ProductAudit product = productAuditRepository.getByProductId(productId);
        AssertUtil.throwIfTrue(product == null || product.getWhetherDelete(), "商品不存在");
        ProductBo productBo = JsonUtil.copy(product, ProductBo.class);
        ProductStatusEnum statusEnum = ProductStatusEnum.getBySaleAuditStatus(product.getSaleStatus(), product.getAuditStatus());
        productBo.setStatus(statusEnum.getCode());
        productBo.setStatusDesc(statusEnum.getDesc());
        productBo.setSourceDesc(ProductSourceEnum.getDescByCode(product.getSource()));

        context.setOldProductBo(productBo);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.QUERY_PRODUCT_AUDIT;
    }

}
