package com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/2/18/018
 * @description:
 */
@Schema(description = "订单优惠券记录响应体")
@Data
public class CouponRecordOrderListResp extends BaseThriftDto {

    @Schema(description = "列表")
    private List<CouponRecordOrderResp> list;


}
