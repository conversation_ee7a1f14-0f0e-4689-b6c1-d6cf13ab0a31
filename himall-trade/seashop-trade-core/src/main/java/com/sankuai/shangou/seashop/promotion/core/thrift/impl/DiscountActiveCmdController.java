package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.core.model.bo.DiscountActiveSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.DiscountActiveService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.DiscountActiveSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.DiscountActiveCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: liuhaox
 * @date: 2023/11/3/003
 * @description:
 */
@RestController
@RequestMapping("/discountActive")
public class DiscountActiveCmdController implements DiscountActiveCmdFeign {

    @Resource
    private DiscountActiveService discountActiveService;

    @PostMapping(value = "/save", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> save(@RequestBody DiscountActiveSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.DISCOUNT_ACTIVE_SAVE_KEY, req.getShopId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                DiscountActiveSaveBo saveBo = JsonUtil.copy(req, DiscountActiveSaveBo.class);
                discountActiveService.save(saveBo);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            discountActiveService.endActive(req);
            return BaseResp.of();
        });
    }
}
