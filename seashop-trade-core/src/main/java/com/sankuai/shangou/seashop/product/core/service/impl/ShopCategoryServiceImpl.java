package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductParam;
import com.sankuai.shangou.seashop.product.common.es.service.EsProductService;
import com.sankuai.shangou.seashop.product.core.mq.publisher.TransferProductPublisher;
import com.sankuai.shangou.seashop.product.core.service.ShopCategoryService;
import com.sankuai.shangou.seashop.product.core.service.assist.ShopCategoryAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryQueryBo;
import com.sankuai.shangou.seashop.product.core.service.model.TransferProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductShopCategoryRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopCategoryRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductSourceEnum;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.type.ProductChangeType;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/13 12:05
 */
@Service
@Slf4j
public class ShopCategoryServiceImpl implements ShopCategoryService {

    @Resource
    private ShopCategoryRepository shopCategoryRepository;
    @Resource
    private ProductShopCategoryRepository productShopCategoryRepository;
    @Resource
    private TransferProductPublisher transferProductPublisher;
    @Resource
    private ProductRepository productRepository;
    @Resource
    private ShopCategoryAssist shopCategoryAssist;
    @Resource
    private EsProductService esProductService;


    @Override
    public void saveShopCategory(ShopCategoryBo shopCategoryBo) {
        String lock = LockConstant.SHOP_CATEGORY_SAVE_LOCK + shopCategoryBo.getShopId() + StrUtil.COLON + shopCategoryBo.getParentCategoryId();
        LockHelper.lock(lock, () -> {
            Boolean editFlag = Optional.ofNullable(shopCategoryBo.getId()).filter(id -> id > 0).isPresent();
            checkAndHandleShopCategoryBo(shopCategoryBo, editFlag, shopCategoryBo.getShopId());

            TransactionHelper.doInTransaction(() -> {
                ShopCategory shopCategory = JsonUtil.copy(shopCategoryBo, ShopCategory.class);
                shopCategoryRepository.saveOrUpdate(shopCategory);

                resetShowStatus(shopCategory.getId(), shopCategoryBo.getWhetherShow());
            });
        });
    }

    @Override
    public void createDefaultShopCategories(Long shopId) {
        log.info("【创建默认店铺分类】开始为店铺{}创建默认分类", shopId);

        Date now = new Date();

        // 创建一级分类
        ShopCategory parentCategory = new ShopCategory();
        parentCategory.setShopId(shopId);
        parentCategory.setParentCategoryId(0L);
        parentCategory.setName("默认分类");
        parentCategory.setDisplaySequence(1L);
        parentCategory.setWhetherShow(true);
        parentCategory.setWhetherDelete(false);
        parentCategory.setCreateTime(now);
        parentCategory.setUpdateTime(now);
        parentCategory.setCreateUser(0L);
        parentCategory.setUpdateUser(0L);

        shopCategoryRepository.save(parentCategory);
        log.info("【创建默认店铺分类】创建一级分类成功，分类ID：{}", parentCategory.getId());

        // 创建二级分类（一级分类的子分类）
        ShopCategory childCategory = new ShopCategory();
        childCategory.setShopId(shopId);
        childCategory.setParentCategoryId(parentCategory.getId());
        childCategory.setName("默认子分类");
        childCategory.setDisplaySequence(1L);
        childCategory.setWhetherShow(true);
        childCategory.setWhetherDelete(false);
        childCategory.setCreateTime(now);
        childCategory.setUpdateTime(now);
        childCategory.setCreateUser(0L);
        childCategory.setUpdateUser(0L);

        shopCategoryRepository.save(childCategory);
        log.info("【创建默认店铺分类】创建二级分类成功，分类ID：{}", childCategory.getId());

        log.info("【创建默认店铺分类】店铺{}默认分类创建完成", shopId);
    }

    @Override
    public void deleteShopCategory(Long id, Long shopId) {
        ShopCategory shopCategory = shopCategoryRepository.getById(id);
        AssertUtil.throwIfTrue(shopCategory == null || shopCategory.getWhetherDelete(), "商家分类不存在");
        AssertUtil.throwIfTrue(!shopCategory.getShopId().equals(shopId), "无权操作");

        // 校验店铺分类已经子分类是否关联的商品
        List<Long> childIds = listChildShopCategoryIds(id);
        childIds.add(id);
        EsProductParam param = new EsProductParam();
        param.setShopCategoryIds(childIds);
        long count = esProductService.count(param);

        AssertUtil.throwIfTrue(count > 0, "该分类或子分类下已关联商品，不能删除");

        TransactionHelper.doInTransaction(() -> {
            shopCategoryRepository.logicRemoveByIds(childIds);

            resetSelfShowStatus(shopCategory.getParentCategoryId());
        });
    }

    @Override
//    @ZebraForceMaster
    public List<ShopCategoryBo> queryShopCategory(ShopCategoryQueryBo shopCategoryQueryBo) {
        List<ShopCategory> shopCategoryList = shopCategoryRepository.list(commonQueryWrapperBuild(shopCategoryQueryBo));
        List<ShopCategoryBo> shopCategoryBoList = JsonUtil.copyList(shopCategoryList, ShopCategoryBo.class);

        // 建树
        shopCategoryBoList.sort(Comparator.comparing(ShopCategoryBo::getDisplaySequence).thenComparing(ShopCategoryBo::getCreateTime, Comparator.reverseOrder()));
        Map<Long, List<ShopCategoryBo>> shopCategoryMap = shopCategoryBoList.stream().collect(Collectors.groupingBy(ShopCategoryBo::getParentCategoryId));
        shopCategoryBoList.forEach(item -> {
            List<ShopCategoryBo> childList = shopCategoryMap.get(item.getId());
            item.setChildList(childList);
        });
        Long parentId = Optional.ofNullable(shopCategoryQueryBo.getParentId()).orElse(CommonConstant.DEFAULT_PARENT_ID);
        List<ShopCategoryBo> rootList = shopCategoryMap.getOrDefault(parentId, Collections.EMPTY_LIST);
        return rootList;
    }

    @Override
    public void asyncTransferProduct(TransferProductBo transferProductBo) {
        checkTransferProductBo(transferProductBo);

        transferProductPublisher.sendMessage(transferProductBo);
    }

    @Override
    public void transferProduct(TransferProductBo transferProductBo) {
        Long fromCategoryId = transferProductBo.getFromCategoryId();
        Long toCategoryId = transferProductBo.getToCategoryId();

        boolean end = false;
        while (!end) {
            LambdaQueryWrapper<ProductShopCategory> wrapper = new LambdaQueryWrapper<ProductShopCategory>()
                    .eq(ProductShopCategory::getShopCategoryId, fromCategoryId)
                    .last(CommonConstant.LIMIT + CommonConstant.UPDATE_LIMIT);

            List<ProductShopCategory> relateList = productShopCategoryRepository.list(wrapper);
            if (!CollectionUtils.isEmpty(relateList)) {
                List<Long> autoIds = relateList.stream().map(ProductShopCategory::getId).collect(Collectors.toList());
                List<Long> productIds = relateList.stream().map(ProductShopCategory::getProductId).collect(Collectors.toList());


                TransactionHelper.doInTransaction(() -> {
                    productShopCategoryRepository.batchUpdateRelate(toCategoryId, autoIds);
                    productRepository.updateUpdateTime(productIds);

                    // 推送类目变更事件
                    pushTransferProductEvent(productIds);
                });
            }

            end = CollectionUtils.isEmpty(relateList) || relateList.size() < CommonConstant.UPDATE_LIMIT;
        }
    }

    private void pushTransferProductEvent(List<Long> productIds) {
        try {
            if (CollUtil.isEmpty(productIds)) {
                return;
            }

            Product product = productRepository.getByProductId(productIds.get(0));
            if (product == null) {
                log.warn("[推送商品类目转移事件] 商品不存在, productId: {}", productIds.get(0));
                return;
            }

            // 发送商品变更事件
            SendProductChangeEvent changeEvent = SendProductChangeEvent.build(productIds, product.getShopId(),
                    ProductSourceEnum.MALL, ProductChangeType.TRANSFER_CATEGORY);
            transferProductPublisher.publish(changeEvent);
        } catch (Exception e) {
            log.error("[推送商品类目转移事件] 异常, productIds: {}", productIds, e);
        }
    }

    @Override
    public List<Long> getSubShopCategoryIds(Long shopCategoryId) {
        return shopCategoryAssist.getChildShopCategoryIdsAndSelf(shopCategoryId);
    }

    @Override
    public List<ShopCategory> queryByShopIdCategoryName(ShopCategoryQueryBo shopCategoryQueryBo) {
        List<ShopCategory> list = shopCategoryRepository.list(new LambdaQueryWrapper<ShopCategory>().eq(ShopCategory::getShopId, shopCategoryQueryBo.getShopId())
                .eq(ShopCategory::getName, shopCategoryQueryBo.getName()));
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            return list;
        }
    }

    private void resetShowStatus(Long shopCategoryId, Boolean newShowStatus) {
        if (newShowStatus == null) {
            return;
        }

        ShopCategory shopCategory = shopCategoryRepository.getById(shopCategoryId);
        if (shopCategory == null || shopCategory.getWhetherDelete()) {
            return;
        }

        List<Long> childIds = listChildShopCategoryIds(shopCategoryId);
        childIds.add(shopCategoryId);
        // 开启状态
        if (newShowStatus) {
            List<Long> allIds = new ArrayList<>(childIds);
            allIds.add(shopCategory.getParentCategoryId());
            shopCategoryRepository.updateShowStatus(allIds, true);
            return;
        }

        // 关闭状态
        shopCategoryRepository.updateShowStatus(childIds, false);
        resetSelfShowStatus(shopCategory.getParentCategoryId());
    }

    private void resetSelfShowStatus(Long shopCategory) {
        if (shopCategory == null || shopCategory <= 0) {
            return;
        }

        long count = shopCategoryRepository.count(new LambdaQueryWrapper<ShopCategory>()
                .eq(ShopCategory::getParentCategoryId, shopCategory)
                .eq(ShopCategory::getWhetherShow, Boolean.TRUE)
                .eq(ShopCategory::getWhetherDelete, Boolean.FALSE));
        shopCategoryRepository.updateShowStatus(Arrays.asList(shopCategory), count > 0);
    }

    /**
     * 构建查询条件
     *
     * @param shopCategoryQueryBo 查询参数
     * @return 查询条件
     */
    private LambdaQueryWrapper<ShopCategory> commonQueryWrapperBuild(ShopCategoryQueryBo shopCategoryQueryBo) {
        LambdaQueryWrapper<ShopCategory> wrapper = new LambdaQueryWrapper<ShopCategory>()
                .eq(ShopCategory::getWhetherDelete, Boolean.FALSE)
                .eq(ShopCategory::getShopId, shopCategoryQueryBo.getShopId());
        if (shopCategoryQueryBo.getParentId() != null) {
            wrapper.eq(ShopCategory::getParentCategoryId, shopCategoryQueryBo.getParentId());
        }
        if (shopCategoryQueryBo.getWhetherShow() != null) {
            wrapper.eq(ShopCategory::getWhetherShow, shopCategoryQueryBo.getWhetherShow());
        }
        return wrapper;
    }

    /**
     * 校验并处理店铺分类参数
     *
     * @param shopCategoryBo 保存店铺分类参数
     * @param editFlag       是否是编辑
     * @param shopId         店铺id
     */
    private void checkAndHandleShopCategoryBo(ShopCategoryBo shopCategoryBo, boolean editFlag, Long shopId) {
        shopCategoryBo.setShopId(shopId);

        Long parentCategoryId = Optional.ofNullable(shopCategoryBo.getParentCategoryId()).orElse(CommonConstant.DEFAULT_PARENT_ID);
        shopCategoryBo.setParentCategoryId(parentCategoryId);
        if (editFlag) {
            ShopCategory shopCategory = shopCategoryRepository.getById(shopCategoryBo.getId());
            AssertUtil.throwIfTrue(shopCategory == null || shopCategory.getWhetherDelete(), "商家分类不存在");
            AssertUtil.throwIfTrue(!shopCategory.getShopId().equals(shopId), "无权操作");
            shopCategoryBo.setParentCategoryId(shopCategory.getParentCategoryId());
        }

        // 校验上级信息
        if (!CommonConstant.DEFAULT_PARENT_ID.equals(shopCategoryBo.getParentCategoryId())) {
            ShopCategory parentShopCategory = shopCategoryRepository.getById(shopCategoryBo.getParentCategoryId());
            AssertUtil.throwIfTrue(parentShopCategory == null || parentShopCategory.getWhetherDelete(), "上级分类不存在");
            AssertUtil.throwIfTrue(!parentShopCategory.getShopId().equals(shopId), "无权操作");

            // 上级分类和当前分类名称不能相同
//            AssertUtil.throwIfTrue(shopCategoryBo.getName().equals(parentShopCategory.getName()), "上级分类和当前分类名称不能相同");
        }

        long count = shopCategoryRepository.count(new LambdaQueryWrapper<ShopCategory>()
                .eq(ShopCategory::getParentCategoryId, shopCategoryBo.getParentCategoryId())
                .eq(ShopCategory::getShopId, shopId)
                .eq(ShopCategory::getName, shopCategoryBo.getName())
                .eq(ShopCategory::getWhetherDelete, Boolean.FALSE)
                .ne(editFlag, ShopCategory::getId, shopCategoryBo.getId()));
        AssertUtil.throwIfTrue(count > 0,
                CommonConstant.DEFAULT_PARENT_ID.equals(parentCategoryId) ? "已存在同名一级分类" : "该分类下已存在同名分类");

        // 如果是新增 计算排序
        if (!editFlag) {
            Long maxDisplaySequence = shopCategoryRepository.getMaxDisplaySequence(shopCategoryBo.getParentCategoryId(), shopId);
            shopCategoryBo.setDisplaySequence(maxDisplaySequence == null ? 0 : maxDisplaySequence + 1);
        }
    }

    /**
     * 查询下级店铺分类的集合
     *
     * @param id 上级店铺分类id
     * @return 下级店铺分类id集合
     */
    private List<Long> listChildShopCategoryIds(Long id) {
        return shopCategoryRepository.listObjs(new LambdaQueryWrapper<ShopCategory>()
                .eq(ShopCategory::getParentCategoryId, id).eq(ShopCategory::getWhetherDelete, Boolean.FALSE)
                .select(ShopCategory::getId), item -> Long.parseLong(String.valueOf(item)));
    }

    /**
     * 检查转移商品参数
     *
     * @param transferProductBo 转移商品参数
     */
    private void checkTransferProductBo(TransferProductBo transferProductBo) {
        AssertUtil.throwIfTrue(transferProductBo.getFromCategoryId().equals(transferProductBo.getToCategoryId()), "转移的分类不能相同");
        ShopCategory fromShopCategory = shopCategoryRepository.getById(transferProductBo.getFromCategoryId());
        AssertUtil.throwIfTrue(fromShopCategory == null || fromShopCategory.getWhetherDelete(), "转移的分类不存在");
        AssertUtil.throwIfTrue(CommonConstant.DEFAULT_PARENT_ID.equals(fromShopCategory.getParentCategoryId()), "转移的分类只能为二级分类");

        ShopCategory toShopCategory = shopCategoryRepository.getById(transferProductBo.getToCategoryId());
        AssertUtil.throwIfTrue(toShopCategory == null || toShopCategory.getWhetherDelete(), "转移到的分类不存在");
        AssertUtil.throwIfTrue(CommonConstant.DEFAULT_PARENT_ID.equals(toShopCategory.getParentCategoryId()), "转移到的分类只能为二级分类");
    }
}
