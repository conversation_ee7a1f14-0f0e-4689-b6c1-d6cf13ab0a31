package com.sankuai.shangou.seashop.seller.thrift.core.service.finance;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.request.finance.ApiCashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.response.finance.ApiCashDepositDetailResp;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@InterfaceDoc(
    type = "thrift",
    displayName = "保证金明细查询相关服务",
    description = "保证金明细查询相关服务",
    scenarios = "保证金明细查询相关服务",
    host = ""
)
@ThriftService
public interface ApiCashDepositDetailQueryThriftService {

    /**
     * 通过条件查询保证金明细列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @MethodDoc(
        displayName = "通过条件查询保证金明细列表",
        description = "通过条件查询保证金明细列表",
        parameters = {
            @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "request", description = "请求体", requiredness = Requiredness.REQUIRED)
        },
        returnValueDescription = "查询结果",
        exceptions = {
            @ExceptionDoc(type = TException.class, description = "RPC调用异常")
        }
    )
    @ThriftMethod
    ResultDto<BasePageResp<ApiCashDepositDetailResp>> pageList(ApiCashDepositDetailQueryReq request, ShopUserInfo shopInfo) throws TException;
}
