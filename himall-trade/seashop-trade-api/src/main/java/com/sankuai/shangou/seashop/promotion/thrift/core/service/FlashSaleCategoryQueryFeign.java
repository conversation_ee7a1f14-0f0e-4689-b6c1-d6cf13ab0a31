package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.FlashSaleCategoryResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description: 限时购分类查询服务
 */
@FeignClient(name = "himall-trade",contextId = "FlashSaleCategoryQueryFeign", path = "/himall-trade/flashSaleCategory", url = "${himall-trade.dev.url:}")
public interface FlashSaleCategoryQueryFeign {

    /**
     * 查询限时购分类列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<FlashSaleCategoryResp>> pageList(@RequestBody BasePageReq request) throws TException;
}
