package com.sankuai.shangou.seashop.product.thrift.core.response.product;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 商品sku库存分页信息
 *
 * <AUTHOR>
 */
@Schema(description = "商品sku库存分页信息")
@Data
public class ProductSkuStockResp extends BaseThriftDto {

    @Schema(description = "商品ID", required = true)
    private Long productId;

    @Schema(description = "商品ID_规格1ID_规格2ID_规格3ID", required = true)
    private String skuId;

    @Schema(description = "sku自增id", required = true)
    private Long skuAutoId;

    @Schema(description = "可用库存", required = true)
    private Long stock;

    @Setter
    @Getter
    @Schema(description = "更新时间", required = true)
    private Date updateTime;

    @Schema(description = "sku货品编号", required = true)
    private String skuCode;


}
