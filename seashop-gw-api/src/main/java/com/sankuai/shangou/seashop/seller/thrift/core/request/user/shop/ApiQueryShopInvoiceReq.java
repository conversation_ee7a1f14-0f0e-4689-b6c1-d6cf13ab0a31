package com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:55
 */
@Data
@TypeDoc(description = "店铺发票查询入参")
public class ApiQueryShopInvoiceReq extends BaseParamReq {

    @FieldDoc(description = "店铺ID")
    private Long shopId;


    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("店铺shopId不能为空");
        }
    }
}
