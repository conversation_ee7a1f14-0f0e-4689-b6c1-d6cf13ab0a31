package com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.descriptiontemplate.dto.DescriptionTemplateDetailDto;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:35
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@Schema(description = "查询版式详情返回值")
public class DescriptionTemplateDetailResp extends BaseThriftDto {

    @Schema(description = "版式详情")
    private DescriptionTemplateDetailDto result;


}
