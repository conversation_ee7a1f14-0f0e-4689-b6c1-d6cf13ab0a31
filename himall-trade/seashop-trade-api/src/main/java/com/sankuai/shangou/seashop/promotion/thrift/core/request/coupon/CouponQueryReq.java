package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券查询请求对象")
public class CouponQueryReq extends BasePageReq {

    @Schema(description = "店铺ID")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "优惠券ID")
    private Long couponId;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "店铺ID列表")
    private List<Long> shopIdList;

    @Schema(description = "状态 0-未开始 1-进行中 2-已结束")
    private Integer status;

    @Schema(description = "可以领用的：true-可以领用的 false-不可以领用的（true时包含状态0、1的数据）")
    private Boolean claimable;

    @Schema(description = "领取方式 0 店铺首页 1 积分兑换 2 主动发放")
    private List<Integer> receiveTypeList;


}
