package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.ProductAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 重新计算商品最低销售价(因为存在部分更新的场景，这种请客需要重新校验最低售价)
 *
 * <AUTHOR>
 * @date 2024/03/09 18:11
 */
@Component
@Slf4j
public class SaveProductSalePriceHandler extends AbsProductHandler {

    @Resource
    private ProductAssist productAssist;
    @Resource
    private ProductRepository productRepository;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】重新计算商品最低销售价【start】, context:{}", context);

        ProductBo saveProductBo = context.getSaveProductBo();
        BigDecimal minSalePrice = productAssist.calculateMinSalePrice(saveProductBo.getWhetherOpenLadder(), context.getProductId());
        saveProductBo.setMinSalePrice(minSalePrice);

        Product updProduct = new Product();
        updProduct.setProductId(context.getProductId());
        updProduct.setMinSalePrice(minSalePrice);
        productRepository.updateByProductId(updProduct);

        log.info("【保存商品】重新计算商品最低销售价【end】, context:{}", context);
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT_MIN_SALE_PRICE;
    }
}
