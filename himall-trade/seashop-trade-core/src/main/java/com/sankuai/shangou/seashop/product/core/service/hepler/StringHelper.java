package com.sankuai.shangou.seashop.product.core.service.hepler;


/**
 * <AUTHOR>
 * @date 2024/03/09 17:33
 */
public class StringHelper {

    /**
     * 存放特殊字符的数组
     */
    private static final String[] SPECIAL_CHAR = {"%"};


    /**
     * 转义字符串中的特殊字符
     */
    public static String escapeSpecialChar(String str) {
        if (str == null) {
            return null;
        }
        for (String special : SPECIAL_CHAR) {
            str = str.replace(special, "\\" + special);
        }
        return str;
    }

}
