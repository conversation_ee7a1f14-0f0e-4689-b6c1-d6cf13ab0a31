package com.sankuai.shangou.seashop.product.core.service.hepler;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * 状态转换类, 将前端传递的状态转换为数据库中的状态
 *
 * <AUTHOR>
 * @date 2023/11/18 13:55
 */
public class ProductStatusHelper {

    @Getter
    @Setter
    public static class Status {

        public Integer saleStatusCode;
        public String saleStatusDesc;
        public Integer auditStatusCode;
        private String auditStatusDesc;

    }

    /**
     * 商品状态转换为 销售状态和审核状态
     *
     * @param statusCode 商品状态
     * @return 销售状态和审核状态
     */
    public static Status getStatus(Integer statusCode) {
        if (statusCode != null) {
            ProductStatusEnum statusEnum = ProductStatusEnum.getByCode(statusCode);
            if (statusEnum != null) {
                Status statue = new Status();
                if (statusEnum.getSaleStatus() != null) {
                    statue.setSaleStatusCode(statusEnum.getSaleStatus().getCode());
                    statue.setSaleStatusDesc(statusEnum.getSaleStatus().getDesc());
                }
                if (statusEnum.getAuditStatus() != null) {
                    statue.setAuditStatusCode(statusEnum.getAuditStatus().getCode());
                    statue.setAuditStatusDesc(statusEnum.getAuditStatus().getDesc());
                }
                return statue;
            }
        }
        return null;
    }

    /**
     * 商品状态转换为 销售状态和审核状态
     *
     * @param status 商品状态
     * @return 销售状态和审核状态
     */
    public static Status getStatus(ProductStatusEnum status) {
        if (status == null) {
            return null;
        }
        return getStatus(status.getCode());
    }

    /**
     * 销售状态和审核状态转换为商品状态
     *
     * @param saleStatusCode  销售状态
     * @param auditStatusCode 审核状态
     * @return 商品状态
     */
    public static ProductStatusEnum getProductStatus(Integer saleStatusCode,
                                                     Integer auditStatusCode) {
        return ProductStatusEnum.getBySaleAuditStatus(saleStatusCode, auditStatusCode);
    }

}
