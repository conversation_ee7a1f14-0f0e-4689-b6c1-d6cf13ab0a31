package com.sankuai.shangou.seashop.product.core.service.assist.event.handler;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.product.common.remote.order.OrderQueryRemoteService;
import com.sankuai.shangou.seashop.product.core.mq.model.OrderMessage;
import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.core.service.ProductService;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.AddSaleCountReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.AddProductSaleCountDto;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/10/31 10:15
 */
@Component
@Slf4j
public class PaySuccessHandler extends AbstractOrderHandler {

    @Resource
    private ProductEsBuildService productEsBuildService;
    @Resource
    private OrderQueryRemoteService orderQueryRemoteService;
    @Resource
    private ProductService productService;

    @Override
    public void handle(OrderMessage event) {
        OrderInfoDto order = orderQueryRemoteService.getOrder(event.getOrderId());
        if (order == null || CollUtil.isEmpty(order.getItemList())) {
            log.warn("[订单变动] pay-success 事件处理, 订单不存在, orderId: {}", event.getOrderId());
            return;
        }

        // 添加销量 失败了也没关系，本来这个销量就不准
        try {
            AddSaleCountReq addSaleCountReq  = new AddSaleCountReq();
            List<AddProductSaleCountDto> addSaleProductList = new ArrayList<>();
            order.getItemList().forEach(item -> {
                AddProductSaleCountDto product = new AddProductSaleCountDto();
                product.setProductId(Long.parseLong(item.getProductId()));
                product.setAddSaleCount(item.getQuantity().intValue());
                addSaleProductList.add(product);
            });
            addSaleCountReq.setProductList(addSaleProductList);
            productService.addSales(addSaleCountReq);
            log.info("[订单变动] pay-success 事件处理, 订单: {}, 增加销量: {}", event.getOrderId(), JsonUtil.toJsonString(addSaleProductList));
        } catch (Exception e) {
            log.error("[订单变动] pay-success 事件处理, 订单: {}, 增加销量失败, error: {}", event.getOrderId(), e.getMessage());
        }

        try {
            // 更新商品销量ES
            order.getItemList().forEach(item -> {
                productEsBuildService.buildProductSalesEs(Long.parseLong(item.getProductId()));
            });
        } catch (Exception e) {
            log.error("[订单变动] pay-success 事件处理, 订单: {}, 更新商品销量ES失败, error: {}", event.getOrderId(), e.getMessage());
        }
    }

    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.PAY_SUCCESS;
    }


}
