package com.sankuai.shangou.seashop.product.core.service.model.erp;

import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductStatusEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.erp.ErpTimeType;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/07 8:48
 */
@Getter
@Setter
@Builder
public class ErpQueryProductBo {

    /**
     * erp状态
     */
    private List<ProductStatusEnum> erpStatus;

    /**
     * 时间类型
     */
    private ErpTimeType timeType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 货号
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 销售状态
     */
    private ProductEnum.SaleStatusEnum saleStatus;

    /**
     * sku编码
     */
    private List<String> skuCodes;

    /**
     * sku自增id
     */
    private List<Long> skuAutoIds;

}
