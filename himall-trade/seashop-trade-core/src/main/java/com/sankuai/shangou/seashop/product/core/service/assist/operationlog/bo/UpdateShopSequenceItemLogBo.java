package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 19:08
 */
@Getter
@Setter
public class UpdateShopSequenceItemLogBo {

    @PrimaryField
    @ExaminField(description = "商品id")
    private Long productId;

    @ExaminField(description = "店铺序号")
    private Integer shopDisplaySequence;

}
