package com.sankuai.shangou.seashop.user.account.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.security.annotation.NeedLogin;
import com.sankuai.shangou.seashop.user.account.service.RoleService;
import com.sankuai.shangou.seashop.user.thrift.account.RoleQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleRespList;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
@RestController
@RequestMapping("/account/role")
public class RoleQueryController implements RoleQueryFeign {
    @Resource
    private RoleService roleService;

    @PostMapping(value = "/queryRoleList", consumes = "application/json")
    @Override
    @NeedLogin
    public ResultDto<RoleRespList> queryRoleList(@RequestBody QueryRoleReq queryRoleReq) {
        return ThriftResponseHelper.responseInvoke("queryRoleList", queryRoleReq, req -> roleService.queryRoleList(queryRoleReq));
    }
}
