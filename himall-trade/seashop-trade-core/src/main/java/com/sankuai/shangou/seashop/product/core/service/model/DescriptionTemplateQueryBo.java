package com.sankuai.shangou.seashop.product.core.service.model;

import com.sankuai.shangou.seashop.product.thrift.core.enums.DescriptionTemplatePositionEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:49
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DescriptionTemplateQueryBo {


    /**
     * 版式名称
     */
    private String name;

    /**
     * 版式位置
     */
    private DescriptionTemplatePositionEnum position;

    /**
     * 店铺id
     */
    private Long shopId;

}
