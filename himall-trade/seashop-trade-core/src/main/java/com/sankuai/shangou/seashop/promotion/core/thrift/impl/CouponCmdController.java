package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.aop.MethodAopAnnotation;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.promotion.common.constant.LockConstant;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponReceiveBo;
import com.sankuai.shangou.seashop.promotion.core.model.bo.CouponSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.CouponService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.SendCouponCmdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponReceiveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: liuhaox
 * @date: 2023/11/7/007
 * @description:
 */
@RestController
@RequestMapping("/coupon")
public class CouponCmdController implements CouponCmdFeign {

    @Resource
    private CouponService couponService;

    @PostMapping(value = "/save", consumes = "application/json")
    @Override
    @MethodAopAnnotation(paramName = "request")
    public ResultDto<BaseResp> save(@RequestBody CouponSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.COUPON_SAVE_KEY, req.getShopId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                CouponSaveBo saveBo = JsonUtil.copy(req, CouponSaveBo.class);
                couponService.save(saveBo);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/endActive", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("endActive", request, req -> {
            req.checkParameter();
            couponService.endActive(req);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/receiveCoupon", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> receiveCoupon(@RequestBody CouponReceiveReq request) throws TException {

        return ThriftResponseHelper.responseInvoke("receiveCoupon", request, req -> {
            req.checkParameter();
            String lockKey = String.format(LockConstant.COUPON_RECEIVE_KEY, req.getCouponId(), req.getUserId());
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                CouponReceiveBo receiveBo = JsonUtil.copy(req, CouponReceiveBo.class);
                couponService.receiveCoupon(receiveBo);
            });
            return BaseResp.of();
        });

    }

    @PostMapping(value = "/sendCoupon", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sendCoupon(@RequestBody SendCouponCmdReq request) throws TException {

        return ThriftResponseHelper.responseInvoke("sendCoupon", request, req -> {
            req.checkParameter();
            String lockKey = LockConstant.COUPON_SEND_KEY;
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                couponService.sendCoupon(req);
            });
            return BaseResp.of();
        });
    }
}
