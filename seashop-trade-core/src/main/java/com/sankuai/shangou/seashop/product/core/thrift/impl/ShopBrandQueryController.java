package com.sankuai.shangou.seashop.product.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ShopBrandService;
import com.sankuai.shangou.seashop.product.core.service.model.ShopBrandBo;
import com.sankuai.shangou.seashop.product.core.service.model.ShopBrandQueryBo;
import com.sankuai.shangou.seashop.product.thrift.core.ShopBrandQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.QueryShopBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.ShopBrandListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.brand.dto.ShopBrandDto;

/**
 * <AUTHOR>
 * @date 2023/11/8 11:45
 */
@RestController
@RequestMapping("/shopBrand")
public class ShopBrandQueryController implements ShopBrandQueryFeign {

    @Resource
    private ShopBrandService shopBrandService;

    @PostMapping(value = "/queryShopBrandForPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ShopBrandDto>> queryShopBrandForPage(@RequestBody QueryShopBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopBrandForList", request, req -> {

            BasePageResp<ShopBrandBo> pageResult = shopBrandService.pageShopBrand(req.buildPage(), JsonUtil.copy(req, ShopBrandQueryBo.class));
            return PageResultHelper.transfer(pageResult, ShopBrandDto.class);
        });
    }

    @PostMapping(value = "/queryShopBrandForList", consumes = "application/json")
    @Override
    public ResultDto<ShopBrandListResp> queryShopBrandForList(@RequestBody QueryShopBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopBrandForList", request, req -> {

            List<ShopBrandBo> shopBrandList = shopBrandService.listShopBrand(JsonUtil.copy(req, ShopBrandQueryBo.class));
            return ShopBrandListResp.builder().brandList(JsonUtil.copyList(shopBrandList, ShopBrandDto.class)).build();
        });
    }
}
