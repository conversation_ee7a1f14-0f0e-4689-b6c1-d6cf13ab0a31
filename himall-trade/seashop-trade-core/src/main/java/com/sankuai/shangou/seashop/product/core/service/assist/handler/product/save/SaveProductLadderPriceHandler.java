package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.save;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.hepler.CompareHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductLadderPriceRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 保存商品阶梯价信息
 *
 * <AUTHOR>
 * @date 2023/11/15 18:28
 */
@Component
@Slf4j
public class SaveProductLadderPriceHandler extends AbsSaveProductHandler {

    @Resource
    private ProductLadderPriceRepository productLadderPriceRepository;

    /**
     * 创建阶梯价
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void create(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        List<ProductLadderPriceBo> newLadderPriceList = saveProductBo.getLadderPriceList();
        if (saveProductBo.getWhetherOpenLadder() && !CollectionUtils.isEmpty(newLadderPriceList)) {
            List<ProductLadderPrice> ladderPriceList = JsonUtil.copyList(newLadderPriceList, ProductLadderPrice.class);
            ladderPriceList.forEach(ladderPrice -> ladderPrice.setProductId(context.getProductId()));
            productLadderPriceRepository.saveBatch(ladderPriceList);
        }
    }

    /**
     * 编辑阶梯价
     *
     * @param context 保存商品上下文对象
     */
    @Override
    public void update(ProductContext context) {
        ProductBo saveProductBo = context.getSaveProductBo();
        ProductBo oldProductBo = context.getOldProductBo();

        List<ProductLadderPriceBo> newLadderPriceList = saveProductBo.getLadderPriceList();
        List<ProductLadderPriceBo> oldLadderPriceList = oldProductBo == null ? null : oldProductBo.getLadderPriceList();
        Boolean same = compareLadderPrice(newLadderPriceList, oldLadderPriceList);
        if (!same) {
            context.setNeedAudit(Boolean.TRUE);
        }
    }

    @Override
    public void updateDraft(ProductContext context) {
        productLadderPriceRepository.remove(new LambdaQueryWrapper<ProductLadderPrice>()
                .eq(ProductLadderPrice::getProductId, context.getProductId()));
        create(context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getWhetherOpenLadder() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.SAVE_PRODUCT_LADDER_PRICE;
    }

    @Override
    public String getHandlerName() {
        return "保存阶梯价";
    }


    /**
     * 比较阶梯价是否变化
     * 同样minBath 和 price 都相同才认为是相同的
     *
     * @param newLadderPriceList 新阶梯价集合
     * @param oldLadderPriceList 旧阶梯价集合
     * @return 是否相同
     */
    private Boolean compareLadderPrice(List<ProductLadderPriceBo> newLadderPriceList, List<ProductLadderPriceBo> oldLadderPriceList) {
        return CompareHelper.compareSame(newLadderPriceList, oldLadderPriceList, ProductLadderPriceBo::getMinBath, ProductLadderPriceBo::getPrice);
    }
}
