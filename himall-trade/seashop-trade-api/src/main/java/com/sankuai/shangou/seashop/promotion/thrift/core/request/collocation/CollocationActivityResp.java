package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 9:54
 */
@Data
@Schema(description = "组合购活动返参")
public class CollocationActivityResp extends BaseParamReq {

    @Schema(description = "活动id")
    private String id;

    @Schema(description = "活动标题")
    private String title;

    @Schema(description = "活动开始时间")
    private Date startTime;

    @Schema(description = "活动结束时间")
    private Date endTime;

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "是否主商品")
    private String mainFlag;

    @Schema(description = "活动状态")
    private String statusName;


}
