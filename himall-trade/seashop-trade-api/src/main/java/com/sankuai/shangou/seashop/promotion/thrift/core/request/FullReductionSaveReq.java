package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.constant.PromotionApiConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存满减活动请求对象")
public class FullReductionSaveReq extends BaseParamReq {

    @Schema(description = "主键ID")
    @PrimaryField(title = "活动ID")
    private Long id;

    /**
     * 店铺编号
     */
    @Schema(description = "店铺编号", required = true)
    @ExaminField(description = "店铺编号")
    private Long shopId;

    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称", required = true)
    @ExaminField(description = "店铺名称")
    private String shopName;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称", required = true)
    @ExaminField(description = "活动名称")
    private String activeName;

    /**
     * 单笔订单满减金额门槛
     */
    @Schema(description = "单笔订单满减金额门槛", required = true)
    @ExaminField(description = "单笔订单满减金额门槛")
    private BigDecimal moneyOffCondition;

    /**
     * 单笔订单满减金额
     */
    @Schema(description = "单笔订单满减金额", required = true)
    @ExaminField(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;

    /**
     * 是否叠加优惠
     */
    @Schema(description = "是否叠加优惠", required = true)
    @ExaminField(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", required = true)
    @ExaminField(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", required = true)
    @ExaminField(description = "结束时间")
    private Date endTime;

    @Override
    public void checkParameter() {
        if (null == this.shopId) {
            throw new InvalidParamException("店铺ID不能为空");
        }
        if (StringUtils.isBlank(this.shopName)) {
            throw new InvalidParamException("店铺名称不能为空");
        }
        if (StringUtils.isBlank(this.activeName)) {
            throw new InvalidParamException("活动名称不能为空");
        }
        this.activeName = this.activeName.trim();
        // activeName名字不能为特殊字符
        if (!Pattern.compile(PromotionApiConstant.NORMAL_CHARACTER_REGEX).matcher(this.activeName).matches()) {
            throw new InvalidParamException("活动名称不能包含特殊字符");
        }
        if (null == this.moneyOffCondition || this.moneyOffCondition.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParamException("金额门槛不能为空且不能小于等于0");
        }
        if (null == this.moneyOffFee || this.moneyOffFee.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParamException("满减金额不能为空且不能小于等于0");
        }
        if (this.moneyOffCondition.compareTo(this.moneyOffFee) < 0) {
            throw new InvalidParamException("金额门槛不能小于满减金额");
        }
        if (null == this.moneyOffOverLay) {
            throw new InvalidParamException("是否叠加优惠不能为空");
        }
        if (null == this.startTime) {
            throw new InvalidParamException("开始时间不能为空");
        }
        if (null == this.endTime) {
            throw new InvalidParamException("结束时间不能为空");
        }
        if (this.startTime.after(this.endTime)) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
    }


}
