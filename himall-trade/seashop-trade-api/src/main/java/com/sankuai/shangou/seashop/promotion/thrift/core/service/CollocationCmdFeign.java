package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.AddCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.CancelCollocationReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation.UpdateCollocationReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:02
 * @description 组合购活动查询相关服务
 */
@FeignClient(name = "himall-trade", contextId = "CollocationCmdFeign", path = "/himall-trade/collocation", url = "${himall-trade.dev.url:}")
public interface CollocationCmdFeign {

    /**
     * 新增组合购活动(供应商端)
     */
    @PostMapping(value = "/addCollocation", consumes = "application/json")
    ResultDto<BaseResp> addCollocation(@RequestBody AddCollocationReq request) throws TException;

    /**
     * 修改组合购活动(供应商端)
     */
    @PostMapping(value = "/updateCollocation", consumes = "application/json")
    ResultDto<BaseResp> updateCollocation(@RequestBody UpdateCollocationReq request) throws TException;

    /**
     * 失效组合购活动(供应商端)
     */
    @PostMapping(value = "/cancelCollocation", consumes = "application/json")
    ResultDto<BaseResp> cancelCollocation(@RequestBody CancelCollocationReq req) throws TException;
}
