package com.sankuai.shangou.seashop.m.thrift.user.account.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 商家列表查询入参
 * @author: LXH
 **/
@Data
@ToString
@TypeDoc(description = "商家列表查询入参")
public class ApiQueryMemberPageReq extends BasePageReq {

    @FieldDoc(description = "商家名称")
    private String memberName;

    @FieldDoc(description = "标签")
    private Long label;

    @FieldDoc(description = "是否为供应商")
    private Boolean izSeller;

    @FieldDoc(description = "注册开始时间")
    private Date registerTimeStart;

    @FieldDoc(description = "注册结束时间")
    private Date registerTimeEnd;

    @FieldDoc(description = "商家等级")
    private Integer gradeId;

    @FieldDoc(description = "手机号")
    private String mobile;

    @FieldDoc(description = "昵称")
    private String weChatNick;

    @FieldDoc(description = "状态 0正常 1冻结")
    private Integer status;

    @FieldDoc(description = "商家来源")
    private Integer platform;

    @FieldDoc(description = "是否查询总数 不需要传")
    private Boolean whetherCount = true;

    //购买次数区间
    @FieldDoc(description = "购买次数 最小值")
    private Integer buyCountStart;
    @FieldDoc(description = "购买次数 最大值")
    private Integer buyCountEnd;
    //笔单价区间
    @FieldDoc(description = "笔单价 最小值")
    private Double priceStart;
    @FieldDoc(description = "笔单价 最大值")
    private Double priceEnd;
    //累计消费金额区间
    @FieldDoc(description = "累计消费金额 最小值")
    private Double totalAmountStart;
    @FieldDoc(description = "累计消费金额 最大值")
    private Double totalAmountEnd;


    public Long getRegisterTimeStartLong() {
        return this.date2Long(this.registerTimeStart);
    }


    public void setRegisterTimeStartLong(Long registerTimeStart) {
        this.registerTimeStart = this.long2Date(registerTimeStart);
    }


    public Long getRegisterTimeEndLong() {
        return this.date2Long(this.registerTimeEnd);
    }


    public void setRegisterTimeEndLong(Long registerTimeEnd) {
        this.registerTimeEnd = this.long2Date(registerTimeEnd);
    }


}
