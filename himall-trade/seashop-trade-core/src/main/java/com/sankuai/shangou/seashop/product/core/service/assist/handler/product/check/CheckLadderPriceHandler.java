package com.sankuai.shangou.seashop.product.core.service.assist.handler.product.check;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.service.assist.LadderPriceAssist;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.AbsProductHandler;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.HandlerConfigEnum;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductContext;
import com.sankuai.shangou.seashop.product.core.service.assist.handler.ProductHandlerType;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductBo;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductLadderPriceBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 阶梯价检查处理器
 *
 * <AUTHOR>
 * @date 2023/11/15 19:41
 */
@Component
@Slf4j
public class CheckLadderPriceHandler extends AbsProductHandler {

    @Resource
    private LadderPriceAssist ladderPriceAssist;

    @Override
    protected List<ProductHandlerType> types() {
        return Arrays.asList(ProductHandlerType.CHECK_SAVE_PRODUCT);
    }

    @Override
    protected void handle(ProductContext context) {
        log.info("【保存商品】处理阶梯价【start】, context:{}", context);

        ProductBo productBo = context.getSaveProductBo();
        if (!productBo.getWhetherOpenLadder()) {
            productBo.setLadderPriceList(Collections.EMPTY_LIST);
        }

        List<ProductLadderPriceBo> ladderPriceList = productBo.getLadderPriceList();
        ladderPriceAssist.handleLadderPrice(ladderPriceList);

        log.info("【保存商品】处理阶梯价【end】, context:{}", context);
    }

    @Override
    public boolean support(ProductContext context) {
        return context.getSaveProductBo().getWhetherOpenLadder() != null;
    }

    @Override
    protected HandlerConfigEnum config() {
        return HandlerConfigEnum.CHECK_LADDER_PRICE;
    }
}
