package com.sankuai.shangou.seashop.product.core.service.assist.listener.handler;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.product.core.mq.publisher.ProductChangePublisher;
import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.SendProductChangeEvent;
import com.sankuai.shangou.seashop.product.thrift.core.event.product.ProductChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/25 14:14
 */
@Slf4j
@Component
public class SendProductChangeHandler extends AbstractHandler<SendProductChangeEvent> {

    @Resource
    private ProductChangePublisher productChangePublisher;

    @Override
    public void handle(SendProductChangeEvent body) {
        List<ProductChangeEvent> eventBody = body.getEventBody();
        Date date = new Date();
        eventBody.forEach(event -> event.setDate(date));
        productChangePublisher.sendMessage(eventBody);
    }

}
