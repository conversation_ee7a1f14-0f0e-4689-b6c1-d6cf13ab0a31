package com.sankuai.shangou.seashop.promotion.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.model.bo.AdvanceSaveBo;
import com.sankuai.shangou.seashop.promotion.core.service.AdvanceService;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.AdvanceSaveReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.AdvanceCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@RestController
@RequestMapping("/advance")
public class AdvanceCmdController implements AdvanceCmdFeign {

    @Resource
    private AdvanceService advanceService;

    @PostMapping(value = "/save", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> save(@RequestBody AdvanceSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("save", request, req -> {
            req.checkParameter();
            AdvanceSaveBo saveBo = JsonUtil.copy(req, AdvanceSaveBo.class);
            advanceService.save(saveBo);
            return null;
        });
    }
}
