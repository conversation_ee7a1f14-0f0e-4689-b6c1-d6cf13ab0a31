package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.FullReductionSaveReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description: 提供满减活动操作功能
 */
@FeignClient(name = "himall-trade",contextId = "FullReductionCmdFeign", path = "/himall-trade/fullReduction", url = "${himall-trade.dev.url:}")
public interface FullReductionCmdFeign {

    /**
     * 保存折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody FullReductionSaveReq request) throws TException;

    /**
     * 保存折扣活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/endActive", consumes = "application/json")
    ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException;
}
