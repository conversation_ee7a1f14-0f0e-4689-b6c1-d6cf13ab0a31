package com.sankuai.shangou.seashop.promotion.thrift.core.response;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/3/7/007
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品ID列表返回对象")
public class ProductIdListResp extends BaseThriftDto {

    @Schema(description = "商品ID列表")
    private List<Long> productIdList;


}
