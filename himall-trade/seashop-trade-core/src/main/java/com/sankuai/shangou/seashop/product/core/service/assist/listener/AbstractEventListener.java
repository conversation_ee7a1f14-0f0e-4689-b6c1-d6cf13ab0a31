package com.sankuai.shangou.seashop.product.core.service.assist.listener;

import org.springframework.transaction.event.TransactionPhase;

import com.sankuai.shangou.seashop.product.core.service.assist.listener.event.AbstractTransactionEvent;

/**
 * <AUTHOR>
 * @date 2024/01/25 14:06
 */
public abstract class AbstractEventListener {

    public abstract void listen(AbstractTransactionEvent event);

    public abstract TransactionPhase getTransactionPhase();

}
