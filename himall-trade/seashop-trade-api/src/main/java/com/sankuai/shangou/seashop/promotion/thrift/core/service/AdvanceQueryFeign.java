package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.AdvanceResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:提供弹窗广告查询功能
 */
@FeignClient(name = "himall-trade",contextId = "AdvanceQueryFeign", path = "/himall-trade/advance", url = "${himall-trade.dev.url:}")
public interface AdvanceQueryFeign {

    /**
     * 查询弹窗广告信息
     */
    @GetMapping(value = "/getOne")
    ResultDto<AdvanceResp> getOne() throws TException;
}
