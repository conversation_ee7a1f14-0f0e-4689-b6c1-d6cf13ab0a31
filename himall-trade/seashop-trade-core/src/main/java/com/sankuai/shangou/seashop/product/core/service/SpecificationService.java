package com.sankuai.shangou.seashop.product.core.service;


import cn.hutool.db.PageResult;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateNameReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.CreateValueReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.specification.SpecificationReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationNameResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.specification.SpecificationValueResp;

import java.util.List;

public interface SpecificationService {
    
    void save(SpecificationReq copy);

    List<SpecificationNameResp> getNames(Long shopId);

    List<SpecificationValueResp> getValues(Long nameId);

    Long createName(CreateNameReq copy);

    Long createValue(CreateValueReq copy);

    BasePageResp<SpecificationResp> query(SpecificationReq request);

    void remove(Long shopId, Long nameId);

    void create(SpecificationReq request);
}
