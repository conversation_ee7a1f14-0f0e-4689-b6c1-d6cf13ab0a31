package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.ProductAuditQueryDetailReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.productaudit.QueryProductAuditReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditDetailResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.productaudit.ProductAuditPageResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/17 10:59
 * 商品审核操作服务
 */
@FeignClient(name = "himall-trade", contextId = "ProductAuditQueryFeign", path = "/himall-trade/productAudit", url = "${himall-trade.dev.url:}")
public interface ProductAuditQueryFeign {

    /**
     * 分页查询商品审核的集合
     */
    @PostMapping(value = "/queryProductAudit", consumes = "application/json")
    ResultDto<BasePageResp<ProductAuditPageResp>> queryProductAudit(@RequestBody QueryProductAuditReq request) throws TException;

    /**
     * 查询商品审核详情

     */
    @PostMapping(value = "/queryProductAuditDetail", consumes = "application/json")
    ResultDto<ProductAuditDetailResp> queryProductAuditDetail(@RequestBody ProductAuditQueryDetailReq request) throws TException;

}
