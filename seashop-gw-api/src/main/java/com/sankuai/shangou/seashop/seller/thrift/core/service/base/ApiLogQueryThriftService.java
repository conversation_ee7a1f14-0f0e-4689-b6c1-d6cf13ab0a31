package com.sankuai.shangou.seashop.seller.thrift.core.service.base;

import org.apache.thrift.TException;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiLogDetailReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ApiLogSellerQueryReq;
import com.sankuai.shangou.seashop.seller.thrift.core.request.base.ShopUserInfo;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiLogDetailResp;
import com.sankuai.shangou.seashop.seller.thrift.core.response.base.ApiLogQueryResp;

/**
 * @author： liweisong
 * @create： 2023/12/1 13:57
 */
@InterfaceDoc(displayName = "操作日志查询",
    type = InterfaceDoc.InterfaceType.THRIFT,
    scenarios = "适用于操作日志的查询操作",
    description = "操作日志查询相关接口")
@ThriftService
public interface ApiLogQueryThriftService {

    @MethodDoc(description = "供应商后台-系统-操作日志（查询）", displayName = "操作日志查询", parameters = {
        @ParamDoc(name = "shopInfo", description = "用户信息"), @ParamDoc(name = "logSellerQueryReq", requiredness = Requiredness.REQUIRED, description = "操作日志查询")}, exceptions = {
        @ExceptionDoc(type = TException.class, description = "发生错误时抛出TException")}, returnValueDescription = "操作日志查询", example = "")
    @ThriftMethod
    ResultDto<BasePageResp<ApiLogQueryResp>> pageSellerBaseLog(ApiLogSellerQueryReq logSellerQueryReq, ShopUserInfo shopInfo) throws TException;

    @MethodDoc(description = "供应商后台-系统-操作日志（查看详情）", displayName = "操作日志查看详情", parameters = {
        @ParamDoc(name = "logDetailReq", requiredness = Requiredness.REQUIRED, description = "查看详情")}, exceptions = {
        @ExceptionDoc(type = TException.class, description = "发生错误时抛出TException")}, returnValueDescription = "查看详情", example = "")
    @ThriftMethod
    ResultDto<ApiLogDetailResp> queryBaseLogDetail(ApiLogDetailReq logDetailReq, ShopUserInfo shopInfo) throws TException;
}
