package com.sankuai.shangou.seashop.m.thrift.system.message.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "检查验证码请求类")
public class ApiCheckCodeCmdReq {
    @FieldDoc(description = "联系方式")
    private String contact;
    @FieldDoc(description = "类型")
    private String type;
    @FieldDoc(description = "验证码")
    private String code;


}
