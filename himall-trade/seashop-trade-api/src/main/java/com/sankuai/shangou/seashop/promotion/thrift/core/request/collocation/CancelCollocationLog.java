package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/6 15:55
 */
@Data
@Schema(description = "组合购查询列表返参")
public class CancelCollocationLog extends BaseParamReq {

    @Schema(description = "活动ID")
    @ExaminField(description = "活动ID")
    @PrimaryField
    private Long id;

    @Schema(description = "店铺ID")
    @ExaminField(description = "店铺ID")
    @PrimaryField
    private Long shopId;

    @Schema(description = "活动结束时间")
    @ExaminField(description = "活动结束时间")
    private String endTime;


}
