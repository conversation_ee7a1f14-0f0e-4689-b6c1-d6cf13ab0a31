package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/05/06 11:20
 */
@Getter
@Setter
public class CategoryBindFormLogBo {

    @ExaminField(description = "类目id")
    @PrimaryField
    private Long categoryId;

    @ExaminField(description = "表单名称")
    private String formName;

    public static CategoryBindFormLogBo build(Long categoryId, String formName) {
        CategoryBindFormLogBo bo = new CategoryBindFormLogBo();
        bo.setCategoryId(categoryId);
        bo.setFormName(formName);
        return bo;
    }

}
