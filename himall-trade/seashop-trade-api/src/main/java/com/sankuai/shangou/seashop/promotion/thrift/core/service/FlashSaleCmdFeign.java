package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description: 限时购活动操作服务
 */
@FeignClient(name = "himall-trade",contextId = "FlashSaleCmdFeign", path = "/himall-trade/flashSale", url = "${himall-trade.dev.url:}")
public interface FlashSaleCmdFeign {

    /**
     * 新增限时购活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/add", consumes = "application/json")
    ResultDto<BaseResp> add(@RequestBody FlashSaleAddReq request) throws TException;

    /**
     * 修改限时购活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<BaseResp> update(@RequestBody FlashSaleUpdateReq request) throws TException;

    /**
     * 审核限时购活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/audit", consumes = "application/json")
    ResultDto<BaseResp> audit(@RequestBody FlashSaleAuditReq request) throws TException;

    /**
     * 前端显示限时购活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/show", consumes = "application/json")
    ResultDto<BaseResp> show(@RequestBody FlashSaleShowReq request) throws TException;

    /**
     * 提前结束限时购活动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/endActive", consumes = "application/json")
    ResultDto<BaseResp> endActive(@RequestBody BaseIdReq request) throws TException;

    /**
     * 核销限时购记录
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/consume", consumes = "application/json")
    ResultDto<BaseResp> consume(@RequestBody FlashSaleConsumeReq request) throws TException;

    /**
     * 撤销核销限时购记录
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/cancelConsume", consumes = "application/json")
    ResultDto<BaseResp> cancelConsume(@RequestBody FlashSaleCancelConsumeReq request) throws TException;

    /**
     * 限时购库存退还
     * （针对售后的情况）
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/stockReturn", consumes = "application/json")
    ResultDto<BaseResp> stockReturn(@RequestBody FlashSaleStockReturnReq request) throws TException;

}
