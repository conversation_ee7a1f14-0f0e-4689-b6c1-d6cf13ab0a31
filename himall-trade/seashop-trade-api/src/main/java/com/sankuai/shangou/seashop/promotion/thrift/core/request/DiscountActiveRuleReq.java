package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保存折扣活动规则请求对象")
public class DiscountActiveRuleReq extends BaseParamReq {

    private static final String MIN_DISCOUNT = "1";
    private static final String MAX_DISCOUNT = "10";
    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "活动ID")
    private Long activeId;
    @Schema(description = "满减的条件")
    private BigDecimal quota;
    @Schema(description = "满减的金额")
    private BigDecimal discount;

    @Override
    public void checkParameter() {
        if (null == this.quota || this.quota.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("满减的条件不能为空且必须大于0");
        }
        if (null == this.discount || this.discount.compareTo(new BigDecimal(MIN_DISCOUNT)) < 0 || this.discount.compareTo(new BigDecimal(MAX_DISCOUNT)) > 0) {
            throw new IllegalArgumentException("折扣范围为1-10");
        }
    }


}