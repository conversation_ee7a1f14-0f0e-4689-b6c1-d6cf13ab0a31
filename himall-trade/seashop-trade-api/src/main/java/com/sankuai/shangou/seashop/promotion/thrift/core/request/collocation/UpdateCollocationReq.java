package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 11:14
 */
@Data
@Schema(description = "新增组合购入参")
public class UpdateCollocationReq extends BaseParamReq {

    @Schema(description = "ID自增")
    @PrimaryField
    private Long id;

    @Schema(description = "组合购标题")
    @ExaminField(description = "组合购标题")
    private String title;

    @Schema(description = "开始日期")
    @ExaminField(description = "开始日期")
    private Date startTime;

    @Schema(description = "结束日期")
    @ExaminField(description = "结束日期")
    private Date endTime;

    @Schema(description = "组合描述")
    @ExaminField(description = "组合描述")
    private String shortDesc;

    @Schema(description = "组合购店铺ID")
    @ExaminField(description = "组合购店铺ID")
    private Long shopId;

    @Schema(description = "创建时间")
    @ExaminField(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    @ExaminField(description = "修改时间")
    private Date updateTime;

    @Schema(description = "主商品、搭配商品集合")
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.promotion.dao.core.domain.CollocationProduct", description = "主商品、搭配商品集合")
    private List<UpdateCollocationProductReq> productReqList;

    public void checkParameter() {
        AssertUtil.throwIfNull(id, "id不能为空");
        AssertUtil.throwIfNull(title, "title不能为空");
        AssertUtil.throwIfNull(startTime, "startTime不能为空");
        AssertUtil.throwIfNull(endTime, "endTime不能为空");
        AssertUtil.throwIfNull(shopId, "shopId不能为空");
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(productReqList), "productReqList不能为空");
        AssertUtil.throwIfTrue(productReqList.stream().filter(v -> !v.getMainFlag()).count() > 9, "搭配商品最多只能选择9个");
        if (!StringUtils.isEmpty(shortDesc)) {
            AssertUtil.throwIfTrue(shortDesc.length() > 500, "组合描述不可超过500字");
        }
    }


}
