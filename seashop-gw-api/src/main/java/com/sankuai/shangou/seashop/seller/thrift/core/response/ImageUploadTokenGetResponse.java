package com.sankuai.shangou.seashop.seller.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@Data
@ToString
@TypeDoc(description = "venus图片上传获取token响应")
public class ImageUploadTokenGetResponse {
    /**
     * token
     */

    @FieldDoc(description = "authorization", requiredness = Requiredness.REQUIRED)
    private String authorization;
    /**
     * 过期时间
     */

    @FieldDoc(description = "过期时间", requiredness = Requiredness.REQUIRED)
    private Long expiretime;
}
