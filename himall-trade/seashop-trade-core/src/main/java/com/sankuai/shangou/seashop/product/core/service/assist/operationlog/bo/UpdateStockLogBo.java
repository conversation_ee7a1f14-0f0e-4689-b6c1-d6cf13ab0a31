package com.sankuai.shangou.seashop.product.core.service.assist.operationlog.bo;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.UpdateStockDto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/11 16:43
 */
@Getter
@Setter
public class UpdateStockLogBo {

    @ExaminField(description = "规格列表", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.UpdateStockDto")
    private List<UpdateStockDto> stockList;

    public static UpdateStockLogBo build(List<UpdateStockDto> stockList) {
        UpdateStockLogBo bo = new UpdateStockLogBo();
        bo.setStockList(stockList);
        return bo;
    }

}
