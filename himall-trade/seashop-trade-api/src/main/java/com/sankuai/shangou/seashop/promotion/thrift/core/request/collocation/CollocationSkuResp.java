package com.sankuai.shangou.seashop.promotion.thrift.core.request.collocation;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Data
@Schema(description = "组合购商品SKU")
public class CollocationSkuResp extends BaseParamReq {

    @Schema(description = "组合购商品SKU表主键ID")
    private Long id;

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "商品SkuId")
    private String skuId;

    @Schema(description = "规格")
    private String specName;

    @Schema(description = "组合商品表ID")
    private Long colloProductId;

    @Schema(description = "组合购价格")
    private BigDecimal price;

    @Schema(description = "原始价格")
    private BigDecimal skuPirce;

    @Schema(description = "库存")
    private Long stock;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;


}
