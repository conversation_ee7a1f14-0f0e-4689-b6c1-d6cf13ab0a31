package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/12/19/019
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "商品和店铺ID请求体")
public class ProductAndShopIdReq extends BaseParamReq {

    @Schema(description = "商品ID", required = true)
    private Long productId;

    @Schema(description = "店铺ID", required = true)
    private Long shopId;

    @Schema(description = "用户ID(传了该值则会计算领取状态)")
    private Long userId;

    @Override
    public void checkParameter() {
        if (productId == null) {
            throw new InvalidParamException("productId不能为空");
        }
        if (shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
    }


}
