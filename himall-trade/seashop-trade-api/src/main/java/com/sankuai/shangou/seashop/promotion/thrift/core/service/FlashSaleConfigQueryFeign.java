package com.sankuai.shangou.seashop.promotion.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.ShopIdReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.PlatFlashSaleConfigResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.ShopFlashSaleConfigResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description: 限时购配置查询服务
 */
@FeignClient(name = "himall-trade",contextId = "FlashSaleConfigQueryFeign", path = "/himall-trade/flashSaleConfig", url = "${himall-trade.dev.url:}")
public interface FlashSaleConfigQueryFeign {

    /**
     * 查询平台限时购配置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getPlatConfig")
    ResultDto<PlatFlashSaleConfigResp> getPlatConfig() throws TException;

    /**
     * 查询店铺限时购配置
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getShopConfig", consumes = "application/json")
    ResultDto<ShopFlashSaleConfigResp> getShopConfig(@RequestBody ShopIdReq request) throws TException;
}
