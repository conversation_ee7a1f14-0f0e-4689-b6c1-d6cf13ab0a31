package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.AuditBrandApplyReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.SaveBrandApplyReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 * @Description: 品牌申请服务
 */
@FeignClient(name="himall-trade",contextId = "BrandApplyCmdFeign",path = "/himall-trade/brandApply", url = "${himall-trade.dev.url:}")
public interface BrandApplyCmdFeign {

    /**
     * 新增申请品牌
     */
    @PostMapping(value = "/createBrandApply", consumes = "application/json")
    ResultDto<BaseResp> createBrandApply(@RequestBody SaveBrandApplyReq request) throws TException;

    /**
     * 编辑申请品牌
     */
    @PostMapping(value = "/updateBrandApply", consumes = "application/json")
    ResultDto<BaseResp> updateBrandApply(@RequestBody SaveBrandApplyReq request) throws TException;

    /**
     * 品牌申请审核
     */
    @PostMapping(value = "/auditBrandApply", consumes = "application/json")
    ResultDto<BaseResp> auditBrandApply(@RequestBody AuditBrandApplyReq request) throws TException;

    /**
     * 品牌申请删除
     */
    @PostMapping(value = "/deleteBrandApply", consumes = "application/json")
    ResultDto<BaseResp> deleteBrandApply(@RequestBody BaseIdReq request) throws TException;

}
