package com.sankuai.shangou.seashop.promotion.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/04/30 14:53
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询折扣商品入参")
public class QueryDiscountActiveProductReq extends BasePageReq {

    @Schema(description = "活动id", required = true)
    private Long activeId;

    @Schema(description = "店铺id")
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(activeId, "活动id不能为空");
    }


}
