package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CreateBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.DeleteBrandReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.UpdateBrandReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/11/01 16:12
 * @Description: 品牌操作服务
 */
@FeignClient(name = "himall-trade", contextId = "BrandCmdFeign", path = "/himall-trade/brand", url = "${himall-trade.dev.url:}")
public interface BrandCmdFeign {

    /**
     * 创建品牌
     */
    @PostMapping(value = "/createBrand", consumes = "application/json")
    ResultDto<BaseResp> createBrand(@RequestBody CreateBrandReq request) throws TException;

    /**
     * 更新品牌
     */
    @PostMapping(value = "/updateBrand", consumes = "application/json")
    ResultDto<BaseResp> updateBrand(@RequestBody UpdateBrandReq request) throws TException;

    /**
     * 删除品牌
     */
    @PostMapping(value = "/deleteBrand", consumes = "application/json")
    ResultDto<BaseResp> deleteBrand(@RequestBody DeleteBrandReq request) throws TException;

}
