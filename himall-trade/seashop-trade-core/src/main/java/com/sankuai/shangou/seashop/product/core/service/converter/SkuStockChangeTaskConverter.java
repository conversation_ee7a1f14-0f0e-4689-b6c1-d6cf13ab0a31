package com.sankuai.shangou.seashop.product.core.service.converter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.product.core.service.model.StockTaskBo;
import com.sankuai.shangou.seashop.product.core.service.model.StockTaskInfoBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockLog;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTask;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTaskInfo;

/**
 * <AUTHOR>
 * @date 2023/12/20 10:02
 */
public class SkuStockChangeTaskConverter {

    public static SkuStockTask taskBoConvertToTask(StockTaskBo taskBo) {
        if (taskBo == null) {
            return null;
        }

        SkuStockTask task = new SkuStockTask();
        task.setId(taskBo.getId());
        task.setUpdateType(taskBo.getUpdateType().getCode());
        task.setUpdateWay(taskBo.getUpdateWay().getCode());
        task.setUpdateKey(taskBo.getUpdateKey().getCode());
        task.setBizCode(taskBo.getBizCode());
        task.setSeqCode(taskBo.getSeqCode());
        return task;
    }


    public static List<SkuStockTaskInfo> taskInfoBoConvertToTaskInfo(List<StockTaskInfoBo> taskInfoBoList) {
        if (CollectionUtils.isEmpty(taskInfoBoList)) {
            return Collections.EMPTY_LIST;
        }

        List<SkuStockTaskInfo> taskInfoList = new ArrayList<>();
        taskInfoBoList.forEach(info -> {
            SkuStockTaskInfo taskInfo = new SkuStockTaskInfo();
            taskInfo.setId(info.getId());
            taskInfo.setTaskId(info.getTaskId());
            taskInfo.setStatus(info.getStatus());
            taskInfo.setStock(info.getStock());
            taskInfo.setBeforeStock(info.getBeforeStock());
            taskInfo.setAfterStock(info.getAfterStock());
            taskInfo.setChangeStock(info.getChangeStock());
            taskInfo.setErrorReason(info.getErrorReason());
            taskInfo.setSkuAutoId(info.getSkuAutoId());
            taskInfo.setSkuId(info.getSkuId());
            taskInfo.setSkuCode(info.getSkuCode());
            taskInfo.setShopId(info.getShopId());
            taskInfo.setProductId(info.getProductId());
            taskInfoList.add(taskInfo);
        });
        return taskInfoList;
    }

    public static List<SkuStockLog> taskInfoBoConvertToStockLog(Integer updateWay, List<StockTaskInfoBo> taskInfoBoList) {
       if (CollectionUtils.isEmpty(taskInfoBoList)) {
           return Collections.EMPTY_LIST;
       }

        List<SkuStockLog> logList = new ArrayList<>();
        taskInfoBoList.forEach(taskInfoBo -> {
            SkuStockLog log = new SkuStockLog();
            log.setSkuId(taskInfoBo.getSkuId());
            log.setSkuAutoId(taskInfoBo.getSkuAutoId());
            log.setBeforeStock(taskInfoBo.getBeforeStock());
            log.setStock(taskInfoBo.getAfterStock());
            log.setUpdateType(updateWay);
            logList.add(log);
        });
        return logList;
    }
}
