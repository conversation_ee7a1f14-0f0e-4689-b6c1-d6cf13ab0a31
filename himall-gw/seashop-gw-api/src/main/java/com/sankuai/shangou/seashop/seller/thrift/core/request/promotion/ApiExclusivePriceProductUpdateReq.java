package com.sankuai.shangou.seashop.seller.thrift.core.request.promotion;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.seller.thrift.core.dto.promotion.ApiExclusivePriceProductDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@TypeDoc(description = "专享价活动商品修改对象")
public class ApiExclusivePriceProductUpdateReq extends BaseParamReq {

    @FieldDoc(description = "主键ID", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "供应商Id")
    private Long shopId;

    @FieldDoc(description = "商品列表", requiredness = Requiredness.REQUIRED)
    private List<ApiExclusivePriceProductDto> productList;

    @Override
    public void checkParameter() {
        if (this.id == null) {
            throw new InvalidParamException("id不能为空");
        }
        if (CollUtil.isEmpty(this.productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
