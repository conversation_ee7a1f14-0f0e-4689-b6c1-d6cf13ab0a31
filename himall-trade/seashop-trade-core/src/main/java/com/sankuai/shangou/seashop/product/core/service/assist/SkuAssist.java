package com.sankuai.shangou.seashop.product.core.service.assist;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.LockConstant;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuAuditConverter;
import com.sankuai.shangou.seashop.product.core.service.converter.SkuConverter;
import com.sankuai.shangou.seashop.product.core.service.hepler.SkuIdHelper;
import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.SkuStockRepository;

import cn.hutool.core.util.StrUtil;

/**
 * sku 辅助类
 *
 * <AUTHOR>
 * @date 2023/11/29 15:06
 */
@Component
public class SkuAssist {

    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SkuStockRepository skuStockRepository;
    @Resource
    private SkuAuditRepository skuAuditRepository;
    @Resource
    private SkuStockAuditRepository skuStockAuditRepository;

    /**
     * 查询商品id下的sku集合
     *
     * @param productId 商品id
     * @return sku集合
     */
    public List<ProductSkuBo> getSkuBoList(Long productId) {
        List<Sku> skuList = skuRepository.list(new LambdaQueryWrapper<Sku>().eq(Sku::getProductId, productId));
        List<SkuStock> skuStockList = skuStockRepository.list(new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getProductId, productId));
        Map<String, SkuStock> skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStock::getSkuId, Function.identity(), (k1, k2) -> k2));
        return skuList.stream().map(sku -> SkuConverter.convertToBProductSkuBo(sku, skuStockMap.get(sku.getSkuId()))).collect(Collectors.toList());
    }

    /**
     * 查询商品id下的审核sku集合
     *
     * @param productId 商品id
     * @return sku集合
     */
    public List<ProductSkuBo> getSkuAuditBoList(Long productId) {
        List<SkuAudit> skuList = skuAuditRepository.list(new LambdaQueryWrapper<SkuAudit>().eq(SkuAudit::getProductId, productId));
        List<SkuStockAudit> skuStockList = skuStockAuditRepository.list(new LambdaQueryWrapper<SkuStockAudit>().eq(SkuStockAudit::getProductId, productId));
        Map<String, SkuStockAudit> skuStockMap = skuStockList.stream().collect(Collectors.toMap(SkuStockAudit::getSkuId, Function.identity(), (k1, k2) -> k2));
        return skuList.stream().map(sku -> SkuAuditConverter.convertToBProductSkuBo(sku, skuStockMap.get(sku.getSkuId()))).collect(Collectors.toList());
    }


    /**
     * 获取sku规格值
     * 逗号拼接 spec1Value_spec2Value_spec3Value
     * 如果值为空的跳过
     *
     * @param sku sku
     * @return 规格值
     */
    public String getSpecValue(Sku sku) {
        if (sku == null) {
            return "";
        }

        return getSkuName(sku.getSpec1Value(), sku.getSpec2Value(), sku.getSpec3Value());
    }

    /**
     * 提取总库存
     *
     * @param skuList sku集合
     * @return 库存
     */
    public Long getSumStock(List<ProductSkuBo> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return 0L;
        }
        return skuList.stream().filter(item -> item.getStock() != null).map(ProductSkuBo::getStock).reduce(0L, Long::sum);
    }

    /**
     * 提取第一个安全库存
     *
     * @param skuList sku集合
     * @return 库存
     */
    public Long getFirstSafeStock(List<ProductSkuBo> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return 0L;
        }
        return skuList.stream().filter(item -> item.getSafeStock() != null).map(ProductSkuBo::getSafeStock).findFirst().orElse(0L);
    }

    /**
     * 获取库存锁
     *
     * @param skuAutoIds sku自增id集合
     * @return 锁集合
     */
    public List<LockKey> getStockLockKeys(List<Long> skuAutoIds) {
        if (CollectionUtils.isEmpty(skuAutoIds)) {
            return Collections.emptyList();
        }

        return skuAutoIds.stream()
                .map(skuAutoId -> new LockKey(LockConstant.STOCK_CHANGE_LOCK + skuAutoId, LockConstant.STOCK_CHANGE_SCENE + skuAutoId))
                .collect(Collectors.toList());
    }

    /**
     * 获取库存锁
     *
     * @param skuAutoId sku自增id
     * @return 锁
     */
    public LockKey getStockLockKey(Long skuAutoId) {
        return getStockLockKeys(Arrays.asList(skuAutoId)).get(0);
    }

    /**
     * 获取库存map
     *
     * @param skuAutoIds sku自增id集合
     * @return 库存map
     */
    public Map<Long, Long> getStockMap(List<Long> skuAutoIds) {
        if (CollectionUtils.isEmpty(skuAutoIds)) {
            return Collections.emptyMap();
        }

        List<SkuStock> skuStockList = skuStockRepository.listBySkuAutoIds(skuAutoIds);
        return skuStockList.stream().collect(Collectors.toMap(SkuStock::getSkuAutoId, SkuStock::getStock, (k1, k2) -> k2));
    }

    /**
     * 获取库存
     *
     * @param skuAutoId sku自增id
     * @return 库存
     */
    public Long getStock(Long skuAutoId) {
        return getStockMap(Arrays.asList(skuAutoId)).getOrDefault(skuAutoId, 0L);
    }

    /**
     * 获取sku名称
     *
     * @param skuAutoId sku自增id
     * @return sku名称
     */
    public String getSkuName(Long skuAutoId) {
        Sku sku = skuRepository.getById(skuAutoId);
        if (sku == null) {
            return "";
        }
        return getSkuName(sku.getSpec1Value(), sku.getSpec2Value(), sku.getSpec3Value());
    }

    /**
     * 获取规格id集合
     *
     * @param skuIdList skuId的集合
     * @return 规格id集合
     */
    public List<Long> getSpecIds(List<String> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return Collections.emptyList();
        }

        Set<Long> specIds = new HashSet<>();
        skuIdList.forEach(skuId -> {
            if (StringUtils.isEmpty(skuId)) {
                return;
            }

            SkuIdHelper.SkuIdDto skuIdDto = SkuIdHelper.getSkuIdDto(skuId);
            specIds.add(skuIdDto.getSpec1ValueId());
            specIds.add(skuIdDto.getSpec2ValueId());
            specIds.add(skuIdDto.getSpec3ValueId());
        });
        return specIds.stream().collect(Collectors.toList());
    }

    /**
     * 获取规格id集合
     *
     * @param productId 商品id
     * @param fromAudit 是否从审核表获取
     * @return 规格id集合
     */
    public List<Long> getSpecIds(Long productId, boolean fromAudit) {
        List<String> skuIds;
        if (fromAudit) {
            List<SkuAudit> skuAuditList = skuAuditRepository.list(new LambdaQueryWrapper<SkuAudit>().eq(SkuAudit::getProductId, productId));
            skuIds = skuAuditList.stream().map(SkuAudit::getSkuId).collect(Collectors.toList());
        }
        else {
            List<Sku> skuList = skuRepository.list(new LambdaQueryWrapper<Sku>().eq(Sku::getProductId, productId));
            skuIds = skuList.stream().map(Sku::getSkuId).collect(Collectors.toList());
        }

        return getSpecIds(skuIds);
    }


    /**
     * 拆分规格字符串
     * 入参为规格字符串 例如: 颜色:黑色,白色;尺码:M,X,L;版本:国行
     * 返回为规格linkedHashMap 例如: {颜色=[黑色,白色], 尺码=[M,X,L], 版本=[国行]}
     */
    public Map<String, List<String>> splitSpecStr(String specStr) {
        if (StringUtils.isEmpty(specStr)) {
            return Collections.emptyMap();
        }


        List<String> groupList = Arrays.stream(specStr.split(CommonConstant.SKU_IMPORT_SPEC_SPLIT)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupList)) {
            return Collections.emptyMap();
        }

        Map<String, List<String>> specMap = new LinkedHashMap<>();
        groupList.forEach(spec -> {
            List<String> itemList = Arrays.asList(spec.split(CommonConstant.SKU_IMPORT_SPEC_VALUE_SPLIT));
            if (CollectionUtils.isEmpty(itemList)) {
                return;
            }

            String key = itemList.get(0);
            List<String> valueList = itemList.size() > 1 ? Arrays.asList(itemList.get(1).split(StrUtil.COMMA)) : Collections.EMPTY_LIST;
            specMap.put(key, valueList);
        });
        return specMap;
    }

    /**
     * 生成skuId 为null的字段补0
     *
     * @param productId    商品id
     * @param spec1ValueId 规格1id
     * @param spec2ValueId 规格2id
     * @param spec3ValueId 规格3id
     * @return skuId
     */
    public String generateSkuId(Long productId, Long spec1ValueId, Long spec2ValueId, Long spec3ValueId) {
        return String.format(CommonConstant.SKU_ID_FORMAT,
                ObjectUtils.defaultIfNull(productId, 0),
                ObjectUtils.defaultIfNull(spec1ValueId, 0),
                ObjectUtils.defaultIfNull(spec2ValueId, 0),
                ObjectUtils.defaultIfNull(spec3ValueId, 0));
    }

    /**
     * 拼接skuName
     *
     * @param spec1Value
     * @param spec2Value
     * @param spec3Value
     * @return
     */
    public String getSkuName(String spec1Value, String spec2Value, String spec3Value) {
        List<String> specValues = new ArrayList<>();
        if (StringUtils.isNotEmpty(spec1Value)) {
            specValues.add(spec1Value);
        }
        if (StringUtils.isNotEmpty(spec2Value)) {
            specValues.add(spec2Value);
        }
        if (StringUtils.isNotEmpty(spec3Value)) {
            specValues.add(spec3Value);
        }
        return StringUtils.join(specValues, StrUtil.COMMA);
    }
}
