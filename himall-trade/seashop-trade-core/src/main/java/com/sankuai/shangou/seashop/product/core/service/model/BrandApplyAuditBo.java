package com.sankuai.shangou.seashop.product.core.service.model;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/07 15:15
 */
@Getter
@Setter
@Builder
public class BrandApplyAuditBo extends BaseParamReq {

    /**
     * 申请记录id
     */
    @ExaminField(description = "申请记录id")
    private Long id;

    /**
     * 审核状态 true-通过 false-拒绝
     */
    @ExaminField(description = "审核状态 true-通过 false-拒绝")
    private Boolean passFlag;

    /**
     * 拒绝原因
     */
    @ExaminField(description = "审批备注")
    private String rejectReason;

}
