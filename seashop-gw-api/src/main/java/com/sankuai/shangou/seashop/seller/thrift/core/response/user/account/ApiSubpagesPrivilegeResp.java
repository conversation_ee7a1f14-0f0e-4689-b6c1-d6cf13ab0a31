package com.sankuai.shangou.seashop.seller.thrift.core.response.user.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: LXH
 **/

@Data
@ToString
@TypeDoc(description = "子页面")
@AllArgsConstructor
@NoArgsConstructor
public class ApiSubpagesPrivilegeResp {
    @FieldDoc(description = "主键")
    private Long id;
    @FieldDoc(description = "父级ID")
    private Long parentId;
    @FieldDoc(description = "名称")
    private String name;
    @FieldDoc(description = "链接")
    private String url;


}
