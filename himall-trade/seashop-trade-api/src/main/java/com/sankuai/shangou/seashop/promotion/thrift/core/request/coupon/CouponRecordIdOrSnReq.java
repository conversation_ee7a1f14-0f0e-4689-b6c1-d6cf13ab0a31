package com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.constant.PromotionApiConstant;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/3/003
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "优惠券记录查询请求对象")
public class CouponRecordIdOrSnReq extends BaseParamReq {

    @Schema(description = "优惠券记录ID")
    private List<Long> recordIdList;

    @Schema(description = "优惠券优惠码")
    private List<String> couponSnList;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(this.recordIdList) && CollUtil.isEmpty(this.couponSnList)) {
            throw new InvalidParamException("优惠券记录ID和优惠券优惠码不能同时为空");
        }
        if (CollUtil.isNotEmpty(this.recordIdList) && this.recordIdList.size() > PromotionApiConstant.IN_MAX_NUM) {
            throw new InvalidParamException("优惠券记录ID不能超过" + PromotionApiConstant.IN_MAX_NUM + "个");
        }
        if (CollUtil.isNotEmpty(this.couponSnList) && this.couponSnList.size() > PromotionApiConstant.IN_MAX_NUM) {
            throw new InvalidParamException("优惠券优惠码不能超过" + PromotionApiConstant.IN_MAX_NUM + "个");
        }
    }


}
