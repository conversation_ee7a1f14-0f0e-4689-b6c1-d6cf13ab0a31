package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "退款时买家寄货的物流信息")
public class RefundUserDeliverExpressResp extends BaseThriftDto {

    @FieldDoc(description = "订单号")
    private String orderId;
    @FieldDoc(description = "退款ID")
    private Long refundId;
    @FieldDoc(description = "物流公司名称")
    private String expressCompanyName;
    @FieldDoc(description = "物流单号")
    private String shipOrderNumber;
    @FieldDoc(description = "物流公司编码")
    private String expressCompanyCode;
    @FieldDoc(description = "退款数量")
    private Long refundQuantity;


}
