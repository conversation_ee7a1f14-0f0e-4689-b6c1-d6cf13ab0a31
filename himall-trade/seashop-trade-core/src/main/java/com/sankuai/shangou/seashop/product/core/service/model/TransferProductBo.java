package com.sankuai.shangou.seashop.product.core.service.model;

import java.io.Serializable;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/06 19:24
 */
@Getter
@Setter
@Builder
public class TransferProductBo implements Serializable {

    /**
     * 原类目id
     */
    private Long fromCategoryId;

    /**
     * 目标类目id
     */
    private Long toCategoryId;

    // 无参构造函数
    public TransferProductBo() {}

    // 带参构造函数
    public TransferProductBo(Long fromCategoryId, Long toCategoryId) {
        this.fromCategoryId = fromCategoryId;
        this.toCategoryId = toCategoryId;
    }

}
